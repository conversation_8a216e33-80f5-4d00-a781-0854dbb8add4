worker_processes auto;
pid /tmp/nginx.pid;

events {
  worker_connections 3096;
  use epoll;
  multi_accept on;
}

http {
  client_body_temp_path /tmp/client_body_temp;
  proxy_temp_path /tmp/proxy_temp;
  fastcgi_temp_path /tmp/fastcgi_temp;
  uwsgi_temp_path /tmp/uwsgi_temp;
  scgi_temp_path /tmp/scgi_temp;
  tcp_nodelay on;

  # this is necessary for us to be able to disable request buffering in all cases
  proxy_http_version 1.1;

  upstream core {
{% if internal_tls.enabled %}
    server core:8443;
{% else %}
    server core:8080;
{% endif %}
  }

  upstream portal {
{% if internal_tls.enabled %}
    server portal:8443;
{% else %}
    server portal:8080;
{% endif %}
  }

  log_format timed_combined '$remote_addr - '
    '"$request" $status $body_bytes_sent '
    '"$http_referer" "$http_user_agent" '
    '$request_time $upstream_response_time $pipe';

  access_log /dev/stdout timed_combined;

  map $http_x_forwarded_proto $x_forwarded_proto {
    default $http_x_forwarded_proto;
    ""      $scheme;
  }

  server {
    listen 8080;
    server_tokens off;
    # disable any limits to avoid HTTP 413 for large image uploads
    client_max_body_size 0;

    # Add extra headers
    add_header X-Frame-Options DENY;
    add_header Content-Security-Policy "frame-ancestors 'none'";

    # customized location config file can place to /etc/nginx/etc with prefix harbor.http. and suffix .conf
    include /etc/nginx/conf.d/harbor.http.*.conf;

    location / {
{% if internal_tls.enabled %}
      proxy_pass https://portal/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://portal/;
{% endif %}
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;

      proxy_buffering off;
      proxy_request_buffering off;
    }

    location /c/ {
{% if internal_tls.enabled %}
      proxy_pass https://core/c/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://core/c/;
{% endif %}
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;

      proxy_buffering off;
      proxy_request_buffering off;
      
      proxy_send_timeout 900;
      proxy_read_timeout 900;
    }

    location /api/ {
{% if internal_tls.enabled %}
      proxy_pass https://core/api/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://core/api/;
{% endif %}
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;

      proxy_buffering off;
      proxy_request_buffering off;
    }

    location /v1/ {
      return 404;
    }

    location /v2/ {
{% if internal_tls.enabled %}
      proxy_pass https://core/v2/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://core/v2/;
{% endif %}
      proxy_set_header Host $http_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;
      proxy_buffering off;
      proxy_request_buffering off;

      proxy_send_timeout 900;
      proxy_read_timeout 900;
    }

    location /service/ {
{% if internal_tls.enabled %}
      proxy_pass https://core/service/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://core/service/;
{% endif %}
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;

      proxy_buffering off;
      proxy_request_buffering off;
    }

    location /service/notifications {
      return 404;
    }
  }
  {% if metric.enabled %}
  upstream core_metrics {
    server core:{{ metric.port }};
  }

  upstream js_metrics {
    server jobservice:{{ metric.port }};
  }

  upstream registry_metrics {
    server registry:{{ metric.port }};
  }

  upstream harbor_exporter {
    server exporter:8080;
  }

  server {
    listen 9090;
    location = /metrics {
      if ($arg_comp = core) { proxy_pass http://core_metrics; }
      if ($arg_comp = jobservice) { proxy_pass http://js_metrics; }
      if ($arg_comp = registry) { proxy_pass http://registry_metrics; }
      proxy_pass http://harbor_exporter;
    }
  }
  {% endif %}
}

{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "title": "Info", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-RdYlGr"}, "custom": {"fillOpacity": 70, "lineWidth": 0, "spanNulls": false}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bool"}, "overrides": []}, "gridPos": {"h": 7, "w": 16, "x": 0, "y": 1}, "id": 6, "options": {"alignValue": "left", "legend": {"displayMode": "hidden", "placement": "bottom"}, "mergeValues": true, "rowHeight": 0.8, "showValue": "never", "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_up", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{component}}", "refId": "A"}], "title": "component up status", "type": "state-timeline"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [], "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 1}, "id": 21, "options": {"legend": {"displayMode": "list", "placement": "bottom"}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_project_quota_usage_byte", "format": "time_series", "interval": "", "legendFormat": "{{project_name}}", "refId": "A"}], "title": "<PERSON><PERSON><PERSON>", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 8}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_project_member_total", "interval": "", "legendFormat": "{{project_name}}", "refId": "A"}], "title": "project members", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 8}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_project_repo_total", "interval": "", "legendFormat": "{{project_name}}", "refId": "A"}], "title": "Project repo total", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 8}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_artifact_pulled", "interval": "", "legendFormat": "{{project_name}}", "refId": "A"}], "title": "artifacts pulled", "type": "timeseries"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 10, "panels": [], "title": "Core Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 16}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": false, "expr": "rate(harbor_core_http_request_total[5m])", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{method}} {{operation}}", "refId": "A"}], "title": "harbor core request rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 16}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_core_http_request_duration_seconds{quantile=\"0.9\"}", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{method}} {{operation}}", "refId": "A"}], "title": "API request time 0.9", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 16}, "id": 30, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_core_http_inflight_requests", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "harbor core inflight requests", "type": "timeseries"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 52, "panels": [], "title": "General metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 24}, "id": 70, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "process_open_fds{service=~\"harbor-.*\"}", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "Process opened fd", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 24}, "id": 94, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "rate(process_cpu_seconds_total{service=~\"harbor-.*\"}[5m])", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "Process CPU time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 24}, "id": 66, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "go_threads{service=~\"harbor-.*\"}", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "go threads", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 24}, "id": 56, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "go_goroutines{service=~\"harbor-.*\"}", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "goroutines", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 32}, "id": 62, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "go_memstats_heap_objects{service=~\"harbor-.*\"}", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "Go heap objects", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 32}, "id": 60, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "go_memstats_alloc_bytes{service=~\"harbor-.*\"}", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "go allocated  memory", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 32}, "id": 68, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "go_memstats_next_gc_bytes{service=~\"harbor-.*\"}", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "go next gc bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 40}, "id": 96, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "go_gc_duration_seconds{quantile=\"0.25\",service=~\"harbor-.*\"}", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "go gc time 0.25", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 40}, "id": 54, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "go_gc_duration_seconds{quantile=\"0.5\",service=~\"harbor-.*\"}", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "go gc time 0.5", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 40}, "id": 95, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "go_gc_duration_seconds{quantile=\"0.75\",service=~\"harbor-.*\"}", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "go gc time 0.75", "type": "timeseries"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 48}, "id": 74, "panels": [], "title": "JobService Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "time period from last process of task queue", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 49}, "id": 92, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_task_queue_latency", "interval": "", "legendFormat": "{{type}}", "refId": "A"}], "title": "task latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 49}, "id": 86, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_task_concurrency", "interval": "", "legendFormat": "{{type}} pool={{pool}}", "refId": "A"}], "title": "task concurrency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 49}, "id": 90, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_task_queue_size", "interval": "", "legendFormat": "{{type}}", "refId": "A"}], "title": "task queue pending size", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 57}, "id": 78, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "rate(harbor_task_scheduled_total[1m])", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "tasks per minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 57}, "id": 88, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "harbor_task_scheduled_total", "interval": "", "legendFormat": "{{service}}", "refId": "A"}], "title": "number of running scheduled jobs", "type": "timeseries"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 62}, "id": 32, "panels": [], "title": "Registry Metric", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 63}, "id": 35, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "registry_http_in_flight_requests", "interval": "", "legendFormat": "{{handler}}", "refId": "A"}], "title": "Registry request inflight", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 63}, "id": 36, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "rate(registry_http_requests_total[5m])", "interval": "", "legendFormat": "{{method}} {{handler}}", "refId": "A"}], "title": "Registry request rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 63}, "id": 40, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "histogram_quantile(0.9, rate(registry_http_request_duration_seconds_bucket[10m]))", "interval": "", "legendFormat": "{{method}} {{handler}}", "refId": "A"}], "title": "registry request time 0.9", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 71}, "id": 44, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "histogram_quantile(0.9, rate(registry_http_request_size_bytes_bucket[10m]))", "interval": "", "legendFormat": "{{handler}}", "refId": "A"}], "title": "registry request size 0.9", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 71}, "id": 46, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.6", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "exemplar": true, "expr": "histogram_quantile(0.9, rate(registry_http_response_size_bytes_bucket[10m]))", "interval": "", "legendFormat": "{{handler}}", "refId": "A"}], "title": "registry response size 0.9", "type": "timeseries"}], "refresh": "10s", "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "Prometheus"}, "hide": 0, "includeAll": false, "label": "data source", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"hidden": false}, "timezone": "", "title": "Harbor", "uid": "iRBnfkZhQ", "version": 1, "weekStart": ""}
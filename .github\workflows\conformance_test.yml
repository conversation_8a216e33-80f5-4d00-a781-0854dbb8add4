name: CONFORMANCE_TEST
env:
  DOCKER_COMPOSE_VERSION: 1.23.0

on:
  repository_dispatch:
    types:
      - manual-trigger-conformance
  schedule:
    - cron: '0 6 * * *'

jobs:
  CONFORMANCE_TEST:
    env:
      CONFORMANCE_TEST: true
    runs-on:
      #- self-hosted
      - ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4.1.0
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: Set up Go 1.21
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.8
        id: go
      - uses: actions/checkout@v3
        with:
          path: src/github.com/goharbor/harbor
      - name: before_install
        run: |
          set -x
          cd src/github.com/goharbor/harbor
          pwd
          env
          df -h
          curl -L https://github.com/docker/compose/releases/download/${DOCKER_COMPOSE_VERSION}/docker-compose-`uname -s`-`uname -m` > docker-compose
          chmod +x docker-compose
          sudo mv docker-compose /usr/local/bin
          IP=`hostname -I | awk '{print $1}'`
          echo '{"insecure-registries" : ["'$IP':5000"]}' | sudo tee /etc/docker/daemon.json
          echo "IP=$IP" >> $GITHUB_ENV
          sudo cp ./tests/harbor_ca.crt /usr/local/share/ca-certificates/
          sudo update-ca-certificates
          sudo service docker restart
      - name: install
        run: |
          cd src/github.com/goharbor/harbor
          env
          df -h
          bash ./tests/showtime.sh ./tests/ci/api_common_install.sh $IP DB
      - name: script
        run: |
          echo IP: $IP
          df -h
          cd src/github.com/goharbor/harbor
          bash ./tests/showtime.sh ./tests/ci/conformance_test.sh $IP
          df -h
      - name: upload test result to gs
        run: |
          cd src/github.com/goharbor/harbor
          aws s3 cp ./distribution-spec/conformance/report.html s3://harbor-conformance-test/report.html
        if: always()

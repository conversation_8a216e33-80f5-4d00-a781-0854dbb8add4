---
name: "Auto Assign"

# pull_request_target means that this will run on pull requests, but in
# the context of the base repo. This should mean PRs from forks are supported.
on:
  pull_request_target:
    types: [opened, reopened, ready_for_review]

jobs:
  # Automatically assigns reviewers and owner
  add-reviews:
    runs-on: ubuntu-latest
    steps:
      - name: Set the author of a PR as the assignee
        uses: kentaro-m/auto-assign-action@v2.0.0
        with:
          configuration-path: ".github/auto-assignees.yml"

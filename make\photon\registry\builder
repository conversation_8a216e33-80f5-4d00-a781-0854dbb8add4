#!/bin/bash

set +e

if [ -z $1 ]; then
  error "Please set the 'version' variable"
  exit 1
fi

if [ -z $2 ]; then
  error "Please set the 'distribution_src' variable"
  exit 1
fi

VERSION="$1"
DISTRIBUTION_SRC="$2"

set -e

# the temp folder to store binary file...
mkdir -p binary
rm -rf binary/registry || true

cd `dirname $0`
cur=$PWD

# the temp folder to store distribution source code...
TEMP=`mktemp -d ${TMPDIR-/tmp}/distribution.XXXXXX`
git clone -b $VERSION $DISTRIBUTION_SRC $TEMP

# add patch redis
cd $TEMP
git apply $cur/redis.patch
cd $cur

echo 'build the registry binary ...'
cp Dockerfile.binary $TEMP
docker build -f $TEMP/Dockerfile.binary -t registry-golang $TEMP

echo 'copy the registry binary to local...'
ID=$(docker create registry-golang)
docker cp $ID:/go/src/github.com/docker/distribution/bin/registry binary/registry

docker rm -f $ID
docker rmi -f registry-golang

echo "Build registry binary success, then to build photon image..."
cd $cur
cp $TEMP/cmd/registry/config-example.yml config.yml
rm -rf $TEMP

#!/bin/bash
set -e

# If compiling source code this dir is harbor's make dir.
# If installing harbor via package, this dir is harbor's root dir.
if [[ -n "$HARBOR_BUNDLE_DIR" ]]; then
    harbor_prepare_path=$HARBOR_BUNDLE_DIR
else
    harbor_prepare_path="$( cd "$(dirname "$0")" ; pwd -P )"
fi
echo "prepare base dir is set to ${harbor_prepare_path}"

# Clean up input dir
rm -rf ${harbor_prepare_path}/input
# Create a input dirs
mkdir -p ${harbor_prepare_path}/input
input_dir=${harbor_prepare_path}/input

# Copy harbor.yml to input dir
if [[ ! "$1" =~ ^\-\- ]] && [ -f "$1" ]
then
    cp $1 $input_dir/harbor.yml
    shift
else
    if [ -f "${harbor_prepare_path}/harbor.yml" ];then
        cp ${harbor_prepare_path}/harbor.yml $input_dir/harbor.yml
    else
        echo "no config file: ${harbor_prepare_path}/harbor.yml"
        exit 1
    fi
fi

data_path=$(grep '^[^#]*data_volume:' $input_dir/harbor.yml | awk '{print $NF}')

# If previous secretkeys exist, move it to new location
previous_secretkey_path=/data/secretkey
previous_defaultalias_path=/data/defaultalias

if [ -f $previous_secretkey_path ]; then
    mkdir -p $data_path/secret/keys
    mv $previous_secretkey_path $data_path/secret/keys
fi
if [ -f $previous_defaultalias_path ]; then
    mkdir -p $data_path/secret/keys
    mv $previous_defaultalias_path $data_path/secret/keys
fi


# Create secret dir
secret_dir=${data_path}/secret
config_dir=$harbor_prepare_path/common/config

# Set the prepare base dir, for mac, it should be $HOME, for linux, it should be /
# The certificate and the data directory in harbor.yaml should be sub directories of $HOME when installing Harbor in MacOS
prepare_base_dir=/
if [ "$(uname)" == "Darwin" ]; then
    prepare_base_dir=$HOME
fi

# Run prepare script
docker run --rm -v $input_dir:/input \
                    -v $data_path:/data \
                    -v $harbor_prepare_path:/compose_location \
                    -v $config_dir:/config \
                    -v ${prepare_base_dir}:/hostfs${prepare_base_dir} \
                    --privileged \
                    goharbor/prepare:dev prepare $@

echo "Clean up the input dir"
# Clean up input dir
rm -rf ${harbor_prepare_path}/input

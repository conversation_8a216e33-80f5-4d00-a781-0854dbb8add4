name: CI
env:
   POSTGRESQL_HOST: localhost
   POSTGRESQL_PORT: 5432
   POSTGRESQL_USR: postgres
   POSTGRESQL_PWD: root123
   POSTGRESQL_DATABASE: registry
   DOCKER_COMPOSE_VERSION: 2.27.1
   HARBOR_ADMIN: admin
   HARBOR_ADMIN_PASSWD: Harbor12345
   CORE_SECRET: tempString
   KEY_PATH: "/data/secret/keys/secretkey"
   REDIS_HOST: localhost
   REG_VERSION: v2.7.1-patch-2819-2553
   UI_BUILDER_VERSION: 1.6.0

on:
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'tests/**'
      - '!tests/**.sh'
      - '!tests/apitests/**'
      - '!tests/ci/**'
  push:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'tests/**'
      - '!tests/**.sh'
      - '!tests/apitests/**'
      - '!tests/ci/**'

jobs:
  UTTEST:
    env:
       UTTEST: true
    runs-on:
      #- self-hosted
      - ubuntu-latest
    timeout-minutes: 100
    steps:
      - name: Set up Go 1.23
        uses: actions/setup-go@v5
        with:
           go-version: 1.23.8
        id: go
      - uses: actions/checkout@v3
        with:
         path: src/github.com/goharbor/harbor
      - name: setup env
        run: |
          cd src/github.com/goharbor/harbor
          pwd
          go env
          echo "GOPATH=$(go env GOPATH):$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "$(go env GOPATH)/bin" >> $GITHUB_PATH
          echo "TOKEN_PRIVATE_KEY_PATH=${GITHUB_WORKSPACE}/src/github.com/goharbor/harbor/tests/private_key.pem" >> $GITHUB_ENV
        shell: bash
      - name: before_install
        run: |
          set -x
          cd src/github.com/goharbor/harbor
          pwd
          env
          #sudo apt install -y xvfb
          #xvfb-run ls
          curl -L https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-`uname -s`-`uname -m` > docker-compose
          chmod +x docker-compose
          sudo mv docker-compose /usr/local/bin
          IP=`hostname -I | awk '{print $1}'`
          echo '{"insecure-registries" : ["'$IP':5000"]}' | sudo tee /etc/docker/daemon.json
          echo "IP=$IP" >> $GITHUB_ENV
          sudo cp ./tests/harbor_ca.crt /usr/local/share/ca-certificates/
          sudo update-ca-certificates
          sudo service docker restart
      - name: install
        run: |
          cd src/github.com/goharbor/harbor
          env
          df -h
          bash ./tests/showtime.sh ./tests/ci/ut_install.sh
      - name: script
        run: |
          echo IP: $IP
          df -h
          cd src/github.com/goharbor/harbor
          bash ./tests/showtime.sh ./tests/ci/ut_run.sh $IP
          df -h
      - name: Codecov For BackEnd
        uses: codecov/codecov-action@v4
        with:
          file: ./src/github.com/goharbor/harbor/profile.cov
          flags: unittests

  APITEST_DB:
    env:
      APITEST_DB: true
    runs-on:
      #- self-hosted
      - ubuntu-latest
    timeout-minutes: 100
    steps:
      - name: Set up Go 1.23
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.8
        id: go
      - uses: actions/checkout@v3
        with:
          path: src/github.com/goharbor/harbor
      - name: setup env
        run: |
          cd src/github.com/goharbor/harbor
          pwd
          go env
          echo "GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}" >> $GITHUB_ENV
          echo "GOPATH=$(go env GOPATH):$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "$(go env GOPATH)/bin" >> $GITHUB_PATH
          echo "TOKEN_PRIVATE_KEY_PATH=${GITHUB_WORKSPACE}/src/github.com/goharbor/harbor/tests/private_key.pem" >> $GITHUB_ENV
          IP=`hostname -I | awk '{print $1}'`
          echo "IP=$IP" >> $GITHUB_ENV
        shell: bash
      - name: before_install
        run: |
          set -x
          cd src/github.com/goharbor/harbor
          pwd
          env
          df -h
          #sudo apt install -y xvfb
          #xvfb-run ls
          curl -L https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-`uname -s`-`uname -m` > docker-compose
          chmod +x docker-compose
          sudo mv docker-compose /usr/local/bin
      - name: install
        run: |
          cd src/github.com/goharbor/harbor
          env
          df -h
          docker system prune -a -f
          bash ./tests/showtime.sh ./tests/ci/api_common_install.sh $IP DB
      - name: script
        run: |
          cd src/github.com/goharbor/harbor
          echo IP: $IP
          df -h
          bash ./tests/showtime.sh ./tests/ci/api_run.sh DB $IP
          df -h

  APITEST_DB_PROXY_CACHE:
    env:
      APITEST_DB: true
    runs-on:
      #- self-hosted
      - ubuntu-latest
    timeout-minutes: 100
    steps:
      - name: Set up Go 1.23
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.8
        id: go
      - uses: actions/checkout@v3
        with:
          path: src/github.com/goharbor/harbor
      - name: setup env
        run: |
          cd src/github.com/goharbor/harbor
          pwd
          go env
          echo "GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}" >> $GITHUB_ENV
          echo "GOPATH=$(go env GOPATH):$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "$(go env GOPATH)/bin" >> $GITHUB_PATH
          echo "TOKEN_PRIVATE_KEY_PATH=${GITHUB_WORKSPACE}/src/github.com/goharbor/harbor/tests/private_key.pem" >> $GITHUB_ENV
          IP=`hostname -I | awk '{print $1}'`
          echo "IP=$IP" >> $GITHUB_ENV
        shell: bash
      - name: before_install
        run: |
          set -x
          cd src/github.com/goharbor/harbor
          pwd
          env
          df -h
          #sudo apt install -y xvfb
          #xvfb-run ls
          curl -L https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-`uname -s`-`uname -m` > docker-compose
          chmod +x docker-compose
          sudo mv docker-compose /usr/local/bin
      - name: install
        run: |
          cd src/github.com/goharbor/harbor
          env
          df -h
          docker system prune -a -f
          bash ./tests/showtime.sh ./tests/ci/api_common_install.sh $IP DB
      - name: script
        run: |
          cd src/github.com/goharbor/harbor
          echo IP: $IP
          df -h
          bash ./tests/showtime.sh ./tests/ci/api_run.sh PROXY_CACHE $IP
          df -h

  APITEST_LDAP:
    env:
      APITEST_LDAP: true
    runs-on:
      #- self-hosted
      - ubuntu-latest
    timeout-minutes: 100
    steps:
      - name: Set up Go 1.23
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.8
        id: go
      - uses: actions/checkout@v3
        with:
          path: src/github.com/goharbor/harbor
      - name: setup env
        run: |
          cd src/github.com/goharbor/harbor
          pwd
          go env
          echo "GOPATH=$(go env GOPATH):$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "$(go env GOPATH)/bin" >> $GITHUB_PATH
          echo "TOKEN_PRIVATE_KEY_PATH=${GITHUB_WORKSPACE}/src/github.com/goharbor/harbor/tests/private_key.pem" >> $GITHUB_ENV
          IP=`hostname -I | awk '{print $1}'`
          echo "IP=$IP" >> $GITHUB_ENV
        shell: bash
      - name: before_install
        run: |
          set -x
          cd src/github.com/goharbor/harbor
          pwd
          env
          df -h
          #sudo apt install -y xvfb
          #xvfb-run ls
          curl -L https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-`uname -s`-`uname -m` > docker-compose
          chmod +x docker-compose
          sudo mv docker-compose /usr/local/bin
      - name: install
        run: |
          cd src/github.com/goharbor/harbor
          env
          df -h
          bash ./tests/showtime.sh ./tests/ci/api_common_install.sh $IP LDAP
      - name: script
        run: |
          echo IP: $IP
          df -h
          cd src/github.com/goharbor/harbor
          bash ./tests/showtime.sh ./tests/ci/api_run.sh LDAP $IP
          df -h

  OFFLINE:
    env:
      OFFLINE: true
    runs-on:
      #- self-hosted
      - ubuntu-latest
    timeout-minutes: 100
    steps:
      - name: Set up Go 1.23
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.8
        id: go
      - uses: actions/checkout@v3
        with:
          path: src/github.com/goharbor/harbor
      - name: setup env
        run: |
          cd src/github.com/goharbor/harbor
          pwd
          docker version
          go env
          echo "GOPATH=$(go env GOPATH):$GITHUB_WORKSPACE" >> $GITHUB_ENV
          echo "$(go env GOPATH)/bin" >> $GITHUB_PATH
          echo "TOKEN_PRIVATE_KEY_PATH=${GITHUB_WORKSPACE}/src/github.com/goharbor/harbor/tests/private_key.pem" >> $GITHUB_ENV
        shell: bash
      - name: before_install
        run: |
          set -x
          cd src/github.com/goharbor/harbor
          pwd
          env
          df -h
          #sudo apt install -y xvfb
          #xvfb-run ls
          curl -L https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-`uname -s`-`uname -m` > docker-compose
          chmod +x docker-compose
          sudo mv docker-compose /usr/local/bin
          IP=`hostname -I | awk '{print $1}'`
          echo '{"insecure-registries" : ["'$IP':5000"]}' | sudo tee /etc/docker/daemon.json
          echo "IP=$IP" >> $GITHUB_ENV
          sudo cp ./tests/harbor_ca.crt /usr/local/share/ca-certificates/
          sudo update-ca-certificates
          sudo service docker restart
      - name: script
        run: |
          echo IP: $IP
          df -h
          cd src/github.com/goharbor/harbor
          bash ./tests/showtime.sh ./tests/ci/distro_installer.sh
          df -h

  UI_UT:
    env:
      UI_UT: true
    runs-on:
      #- self-hosted
      - ubuntu-latest
    timeout-minutes: 100
    steps:
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
      - uses: actions/checkout@v3
        with:
          path: src/github.com/goharbor/harbor
      - name: script
        run: |
          echo IP: $IP
          df -h
          cd src/github.com/goharbor/harbor
          bash ./tests/showtime.sh ./tests/ci/ui_ut_run.sh
          df -h
      - name: Codecov For UI
        uses: codecov/codecov-action@v4
        with:
          file:  ./src/github.com/goharbor/harbor/src/portal/coverage/lcov.info
          flags: unittests

worker_processes auto;
pid /tmp/nginx.pid;

events {
  worker_connections 3096;
  use epoll;
  multi_accept on;
}

http {
  client_body_temp_path /tmp/client_body_temp;
  proxy_temp_path /tmp/proxy_temp;
  fastcgi_temp_path /tmp/fastcgi_temp;
  uwsgi_temp_path /tmp/uwsgi_temp;
  scgi_temp_path /tmp/scgi_temp;
  tcp_nodelay on;
  include /etc/nginx/conf.d/*.upstream.conf;

  # this is necessary for us to be able to disable request buffering in all cases
  proxy_http_version 1.1;

  upstream core {
{% if internal_tls.enabled %}
    server core:8443;
{% else %}
    server core:8080;
{% endif %}
  }

  upstream portal {
{% if internal_tls.enabled %}
    server portal:8443;
{% else %}
    server portal:8080;
{% endif %}
  }

  log_format timed_combined '$remote_addr - '
    '"$request" $status $body_bytes_sent '
    '"$http_referer" "$http_user_agent" '
    '$request_time $upstream_response_time $pipe';

  access_log /dev/stdout timed_combined;

  map $http_x_forwarded_proto $x_forwarded_proto {
    default $http_x_forwarded_proto;
    ""      $scheme;
  }

  include /etc/nginx/conf.d/*.server.conf;

  server {
    {% if ip_family.ipv4.enabled %}
    listen 8443 ssl;
    {% endif %}
    {% if ip_family.ipv6.enabled %}
    listen [::]:8443 ssl;
    {% endif %}
#    server_name harbordomain.com;
    server_tokens off;
    # SSL
    ssl_certificate {{ssl_cert}};
    ssl_certificate_key {{ssl_cert_key}};

    # Recommendations from https://raymii.org/s/tutorials/Strong_SSL_Security_On_nginx.html
    ssl_protocols TLSv1.2 TLSv1.3;
{% if strong_ssl_ciphers %}
    ssl_ciphers ECDHE+AESGCM:DHE+AESGCM:ECDHE+RSA+SHA256:DHE+RSA+SHA256:!AES128;
{% else %}
    ssl_ciphers '!aNULL:kECDH+AESGCM:ECDH+AESGCM:RSA+AESGCM:kECDH+AES:ECDH+AES:RSA+AES:';
{% endif %}
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;

    # disable any limits to avoid HTTP 413 for large image uploads
    client_max_body_size 0;

    # required to avoid HTTP 411: see Issue #1486 (https://github.com/docker/docker/issues/1486)
    chunked_transfer_encoding on;

    # Add extra headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubdomains; preload";
    add_header X-Frame-Options DENY;
    add_header Content-Security-Policy "frame-ancestors 'none'";

    # customized location config file can place to /etc/nginx dir with prefix harbor.https. and suffix .conf
    include /etc/nginx/conf.d/harbor.https.*.conf;

    location / {
{% if internal_tls.enabled %}
      proxy_pass https://portal/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://portal/;
{% endif %}
      proxy_set_header Host $http_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;

      proxy_cookie_path / "/; HttpOnly; Secure";

      proxy_buffering off;
      proxy_request_buffering off;
    }

    location /c/ {
{% if internal_tls.enabled %}
      proxy_pass https://core/c/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://core/c/;
{% endif %}
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;

      proxy_cookie_path / "/; Secure";

      proxy_buffering off;
      proxy_request_buffering off;
    }

    location /api/ {
{% if internal_tls.enabled %}
      proxy_pass https://core/api/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://core/api/;
{% endif %}
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;

      proxy_cookie_path / "/; Secure";

      proxy_buffering off;
      proxy_request_buffering off;
    }

    location /v1/ {
      return 404;
    }

    location /v2/ {
{% if internal_tls.enabled %}
      proxy_pass https://core/v2/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://core/v2/;
{% endif %}
      proxy_set_header Host $http_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;
      proxy_buffering off;
      proxy_request_buffering off;
      proxy_send_timeout 900;
      proxy_read_timeout 900;
    }

    location /service/ {
{% if internal_tls.enabled %}
      proxy_pass https://core/service/;

      proxy_ssl_certificate         /etc/harbor/tls/proxy.crt;
      proxy_ssl_certificate_key     /etc/harbor/tls/proxy.key;
      proxy_ssl_trusted_certificate /harbor_cust_cert/harbor_internal_ca.crt;
      proxy_ssl_verify_depth 2;
      proxy_ssl_verify        on;
      proxy_ssl_session_reuse on;
{% else %}
      proxy_pass http://core/service/;
{% endif %}
      proxy_set_header Host $http_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $x_forwarded_proto;

      proxy_cookie_path / "/; Secure";

      proxy_buffering off;
      proxy_request_buffering off;
    }

    location /service/notifications {
      return 404;
    }
  }
  server {
      listen 8080;
      #server_name harbordomain.com;
      return 308 https://{{https_redirect}}$request_uri;
  }
  {% if metric.enabled %}
  upstream core_metrics {
    server core:{{ metric.port }};
  }

  upstream js_metrics {
    server jobservice:{{ metric.port }};
  }

  upstream registry_metrics {
    server registry:{{ metric.port }};
  }

  upstream harbor_exporter {
    server exporter:8080;
  }

  server {
    listen 9090;
    location = {{ metric.path }} {
      if ($arg_comp = core) { proxy_pass http://core_metrics; }
      if ($arg_comp = jobservice) { proxy_pass http://js_metrics; }
      if ($arg_comp = registry) { proxy_pass http://registry_metrics; }
      proxy_pass http://harbor_exporter;
    }
  }
  {% endif %}
}

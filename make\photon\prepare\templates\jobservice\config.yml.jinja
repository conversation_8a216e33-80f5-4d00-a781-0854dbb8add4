---
#Protocol used to serve
{% if internal_tls.enabled %}
protocol: "https"

https_config:
  cert: "/etc/harbor/ssl/job_service.crt"
  key: "/etc/harbor/ssl/job_service.key"

#Server listening port
port: 8443
{% else %}
protocol: "http"

#Server listening port
port: 8080
{% endif %}

#Worker pool
worker_pool:
  #Worker concurrency
  workers: {{max_job_workers}}
  backend: "redis"
  #Additional config if use 'redis' backend
  redis_pool:
    #redis://[arbitrary_username:password@]ipaddress:port/database_index
    redis_url: {{redis_url}}
    namespace: "harbor_job_service_namespace"
    idle_timeout_second: 3600
#Loggers for the running job
job_loggers:
  # The jobLoggers backend name, only support "STD_OUTPUT", "FILE" and/or "DB"
  {% for component in job_loggers %}
    {% if component == 'STD_OUTPUT' %}
  - name: "STD_OUTPUT"
    level: "{{level}}" # INFO/DEBUG/WARNING/ERROR/FATAL
    {% endif %}
    {% if component == 'FILE' %}
  - name: "FILE"
    level: "{{level}}"
    settings: # Customized settings of logger
      base_dir: "/var/log/jobs"
    sweeper:
      duration: {{logger_sweeper_duration}} #days
      settings: # Customized settings of sweeper
        work_dir: "/var/log/jobs"
    {% endif %}
    {% if component == 'DB' %}
  - name: "DB"
    level: "{{level}}"
    sweeper:
      duration: {{logger_sweeper_duration}} #days
    {% endif %}
  {% endfor %}

#Loggers for the job service
loggers:
  - name: "STD_OUTPUT" # Same with above
    level: "{{level}}"

{% if metric.enabled %}
metric:
  enabled: true
  path: {{ metric.path }}
  port: {{ metric.port }}
{% endif %}

reaper:
  # the max time to wait for a task to finish, if unfinished after max_update_hours, the task will be mark as error, but the task will continue to run, default value is 24,
  max_update_hours: 24
  # the max time for execution in running state without new task created
  max_dangling_hours: 168

# the max size of job log returned by API, default is 10M
max_retrieve_size_mb: 10
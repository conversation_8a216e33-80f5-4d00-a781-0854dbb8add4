services:
  log:
    image: goharbor/harbor-log:{{version}}
    container_name: harbor-log
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - DAC_OVERRIDE
      - SETGID
      - SETUID
    volumes:
      - {{log_location}}/:/var/log/docker/:z
      - type: bind
        source: ./common/config/log/logrotate.conf
        target: /etc/logrotate.d/logrotate.conf
      - type: bind
        source: ./common/config/log/rsyslog_docker.conf
        target: /etc/rsyslog.d/rsyslog_docker.conf
    ports:
      - 127.0.0.1:1514:10514
    networks:
      - harbor
  registry:
    image: goharbor/registry-photon:{{reg_version}}
    container_name: registry
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    volumes:
      - {{data_volume}}/registry:/storage:z
      - ./common/config/registry/:/etc/registry/:z
      - type: bind
        source: {{data_volume}}/secret/registry/root.crt
        target: /etc/registry/root.crt
      - type: bind
        source: ./common/config/shared/trust-certificates
        target: /harbor_cust_cert
{% if gcs_keyfile %}
      - type: bind
        source: {{gcs_keyfile}}
        target: /etc/registry/gcs.key
{% endif %}
{%if internal_tls.enabled %}
      - type: bind
        source: {{internal_tls.core_crt_path}}
        target: /harbor_cust_cert/core.crt
      - type: bind
        source: {{internal_tls.registry_crt_path}}
        target: /etc/harbor/tls/registry.crt
      - type: bind
        source: {{internal_tls.registry_key_path}}
        target: /etc/harbor/tls/registry.key
{% endif %}
    networks:
      - harbor
    depends_on:
      - log
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "registry"
  registryctl:
    image: goharbor/harbor-registryctl:{{version}}
    container_name: registryctl
    env_file:
      - ./common/config/registryctl/env
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    volumes:
      - {{data_volume}}/registry:/storage:z
      - ./common/config/registry/:/etc/registry/:z
      - type: bind
        source: ./common/config/registryctl/config.yml
        target: /etc/registryctl/config.yml
      - type: bind
        source: ./common/config/shared/trust-certificates
        target: /harbor_cust_cert
{% if gcs_keyfile %}
      - type: bind
        source: {{gcs_keyfile}}
        target: /etc/registry/gcs.key
{% endif %}
{%if internal_tls.enabled %}
      - type: bind
        source: {{internal_tls.registryctl_crt_path}}
        target: /etc/harbor/ssl/registryctl.crt
      - type: bind
        source: {{internal_tls.registryctl_key_path}}
        target: /etc/harbor/ssl/registryctl.key
{% endif %}
    networks:
      - harbor
    depends_on:
      - log
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "registryctl"
{% if external_database == False %}
  postgresql:
    image: goharbor/harbor-db:{{version}}
    container_name: harbor-db
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - DAC_OVERRIDE
      - SETGID
      - SETUID
    volumes:
      - {{data_volume}}/database:/var/lib/postgresql/data:z
    networks:
      harbor:
    env_file:
      - ./common/config/db/env
    depends_on:
      - log
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "postgresql"
    shm_size: '1gb'
{% endif %}
  core:
    image: goharbor/harbor-core:{{version}}
    container_name: harbor-core
    env_file:
      - ./common/config/core/env
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - SETGID
      - SETUID
    volumes:
      - {{data_volume}}/ca_download/:/etc/core/ca/:z
      - {{data_volume}}/:/data/:z
      - ./common/config/core/certificates/:/etc/core/certificates/:z
      - type: bind
        source: ./common/config/core/app.conf
        target: /etc/core/app.conf
      - type: bind
        source: {{data_volume}}/secret/core/private_key.pem
        target: /etc/core/private_key.pem
      - type: bind
        source: {{data_volume}}/secret/keys/secretkey
        target: /etc/core/key
      - type: bind
        source: ./common/config/shared/trust-certificates
        target: /harbor_cust_cert
{% if uaa_ca_file %}
      - type: bind
        source: {{uaa_ca_file}}
        target: /etc/core/certificates/uaa_ca.pem
{% endif %}
{%if internal_tls.enabled %}
      - type: bind
        source: {{internal_tls.core_crt_path}}
        target: /etc/harbor/ssl/core.crt
      - type: bind
        source: {{internal_tls.core_key_path}}
        target: /etc/harbor/ssl/core.key
{% endif %}
    networks:
      harbor:
    depends_on:
      - log
      - registry
{% if external_redis == False %}
      - redis
{% endif %}
{% if external_database == False %}
      - postgresql
{% endif %}
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "core"
  portal:
    image: goharbor/harbor-portal:{{version}}
    container_name: harbor-portal
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - NET_BIND_SERVICE
    volumes:
      - type: bind
        source: ./common/config/portal/nginx.conf
        target: /etc/nginx/nginx.conf
{%if internal_tls.enabled %}
      - type: bind
        source: {{internal_tls.portal_crt_path}}
        target: /etc/harbor/tls/portal.crt
      - type: bind
        source: {{internal_tls.portal_key_path}}
        target: /etc/harbor/tls/portal.key
{% endif %}
    networks:
      - harbor
    depends_on:
      - log
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "portal"

  jobservice:
    image: goharbor/harbor-jobservice:{{version}}
    container_name: harbor-jobservice
    env_file:
      - ./common/config/jobservice/env
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    volumes:
      - {{data_volume}}/job_logs:/var/log/jobs:z
      - type: bind
        source: ./common/config/jobservice/config.yml
        target: /etc/jobservice/config.yml
      - type: bind
        source: ./common/config/shared/trust-certificates
        target: /harbor_cust_cert
{%if internal_tls.enabled %}
      - type: bind
        source: {{internal_tls.job_service_crt_path}}
        target: /etc/harbor/ssl/job_service.crt
      - type: bind
        source: {{internal_tls.job_service_key_path}}
        target: /etc/harbor/ssl/job_service.key
{% endif %}
    networks:
      - harbor
    depends_on:
      - core
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "jobservice"
{% if external_redis == False %}
  redis:
    image: goharbor/redis-photon:{{redis_version}}
    container_name: redis
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
    volumes:
      - {{data_volume}}/redis:/var/lib/redis
    networks:
      harbor:
    depends_on:
      - log
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "redis"
{% endif %}
  proxy:
    image: goharbor/nginx-photon:{{version}}
    container_name: nginx
    restart: always
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
      - NET_BIND_SERVICE
    volumes:
      - ./common/config/nginx:/etc/nginx:z
{% if protocol == 'https' %}
      - {{data_volume}}/secret/cert:/etc/cert:z
{% endif %}
      - type: bind
        source: ./common/config/shared/trust-certificates
        target: /harbor_cust_cert
{%if internal_tls.enabled %}
      - type: bind
        source: {{internal_tls.proxy_crt_path}}
        target: /etc/harbor/tls/proxy.crt
      - type: bind
        source: {{internal_tls.proxy_key_path}}
        target: /etc/harbor/tls/proxy.key
{% endif %}
    networks:
      - harbor
    ports:
      - {{http_port}}:8080
{% if protocol == 'https' %}
      - {{https_port}}:8443
{% endif %}
{% if metric.enabled %}
      - {{metric.port}}:9090
{% endif %}
    depends_on:
      - registry
      - core
      - portal
      - log
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "proxy"
{% if with_trivy %}
  trivy-adapter:
    container_name: trivy-adapter
    image: goharbor/trivy-adapter-photon:{{trivy_adapter_version}}
    restart: always
    cap_drop:
      - ALL
    depends_on:
      - log
{% if external_redis == False %}
      - redis
{% endif %}
    networks:
      - harbor
    volumes:
      - type: bind
        source: {{data_volume}}/trivy-adapter/trivy
        target: /home/<USER>/.cache/trivy
      - type: bind
        source: {{data_volume}}/trivy-adapter/reports
        target: /home/<USER>/.cache/reports
      - type: bind
        source: ./common/config/shared/trust-certificates
        target: /harbor_cust_cert
{% if internal_tls.enabled %}
      - type: bind
        source: {{internal_tls.trivy_adapter_crt_path}}
        target: /etc/harbor/ssl/trivy_adapter.crt
      - type: bind
        source: {{internal_tls.trivy_adapter_key_path}}
        target: /etc/harbor/ssl/trivy_adapter.key
{% endif %}
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "trivy-adapter"
    env_file:
      ./common/config/trivy-adapter/env
{% endif %}
{% if metric.enabled %}
  exporter:
    image: goharbor/harbor-exporter:{{version}}
    container_name: harbor-exporter
    env_file:
      - ./common/config/exporter/env
    restart: always
    networks:
      - harbor
    depends_on:
      - core
  {% if external_database == False %}
      - postgresql
  {% endif %}
    volumes:
      - type: bind
        source: ./common/config/shared/trust-certificates
        target: /harbor_cust_cert
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://localhost:1514"
        tag: "exporter"
{% endif %}
networks:
  harbor:
    external: false


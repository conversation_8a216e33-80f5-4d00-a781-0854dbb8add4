# Configuration file of Harbor

# The IP address or hostname to access admin UI and registry service.
# DO NOT use localhost or 127.0.0.1, because Harbor needs to be accessed by external clients.
hostname: {{ hostname }}

# http related config
{% if http %}
http:
  # port for http, default is 80. If https enabled, this port will redirect to https port
  port: {{ http.port }}
{% endif %}

{% if https is defined %}
# https related config
https:
  # https port for harbor, default is 443
  port: {{ https.port }}
  # The path of cert and key files for nginx
  certificate: {{ https.certificate }}
  private_key: {{ https.private_key }}
{% else %}
# https related config
# https:
#   # https port for harbor, default is 443
#   port: 443
#   # The path of cert and key files for nginx
#   certificate: /your/certificate/path
#   private_key: /your/private/key/path
{% endif %}

# Uncomment external_url if you want to enable external proxy
# And when it enabled the hostname will no longer used
{% if external_url is defined %}
external_url: {{ external_url }}
{% else %}
# external_url: https://reg.mydomain.com:8433
{% endif %}

{% if harbor_admin_password is defined %}
# The initial password of Harbor admin
# It only works in first time to install harbor
# Remember Change the admin password from UI after launching Harbor.
harbor_admin_password: {{ harbor_admin_password }}
{% endif %}

{% if database is defined %}
# Harbor DB configuration
database:
  # The password for the root user of Harbor DB. Change this before any production use.
  password: {{ database.password }}
  # The maximum number of connections in the idle connection pool. If it <=0, no idle connections are retained.
  max_idle_conns: 50
  # The maximum number of open connections to the database. If it <= 0, then there is no limit on the number of open connections.
  # Note: the default number of connections is 100 for postgres.
  max_open_conns: 100
{% endif %}

{% if data_volume is defined %}
# The default data volume
data_volume: {{ data_volume }}
{% endif %}

{% if storage_service is defined %}
# Harbor Storage settings by default is using /data dir on local filesystem
# Uncomment storage_service setting If you want to using external storage
storage_service:
  {% for key, value in storage_service.items() %}
    {% if key == 'ca_bundle' %}
#   # ca_bundle is the path to the custom root ca certificate, which will be injected into the truststore
#   # of registry's and chart repository's containers.  This is usually needed when the user hosts a internal storage with self signed certificate.
  ca_bundle: {{ value if value is not none else '' }}
    {% elif key == 'redirect' %}
#   # set disable to true when you want to disable registry redirect
  redirect:
    disabled: {{ value.disabled }}
    {% else %}
#   # storage backend, default is filesystem, options include filesystem, azure, gcs, s3, swift and oss
#   # for more info about this configuration please refer https://docs.docker.com/registry/configuration/
  {{ key }}:
      {% for k, v in value.items() %}
    {{ k }}: {{ v if v is not none else '' }}
      {% endfor %}
    {% endif %}
  {% endfor %}
{% else %}
# Harbor Storage settings by default is using /data dir on local filesystem
# Uncomment storage_service setting If you want to using external storage
# storage_service:
#   # ca_bundle is the path to the custom root ca certificate, which will be injected into the truststore
#   # of registry's and chart repository's containers.  This is usually needed when the user hosts a internal storage with self signed certificate.
#   ca_bundle:

#   # storage backend, default is filesystem, options include filesystem, azure, gcs, s3, swift and oss
#   # for more info about this configuration please refer https://docs.docker.com/registry/configuration/
#   filesystem:
#     maxthreads: 100
#   # set disable to true when you want to disable registry redirect
#   redirect:
#     disabled: false
{% endif %}

{% if clair is defined %}
# Clair configuration
clair:
  # The interval of clair updaters, the unit is hour, set to 0 to disable the updaters.
  updaters_interval: {{ clair.updaters_interval }}
{% endif %}

{% if jobservice is defined %}
jobservice:
  # Maximum number of job workers in job service
  max_job_workers: {{ jobservice.max_job_workers }}
{% endif %}

notification:
  # Maximum retry count for webhook job
  webhook_job_max_retry: 10

{% if chart is defined %}
chart:
  # Change the value of absolute_url to enabled can enable absolute url in chart
  absolute_url: {{ chart.absolute_url if chart.absolute_url == 'enabled' else 'disabled' }}
{% endif %}

# Log configurations
log:
  # options are debug, info, warning, error, fatal
  level: {{ log.level }}
  # configs for logs in local storage
  local:
    # Log files are rotated log_rotate_count times before being removed. If count is 0, old versions are removed rather than rotated.
    rotate_count: {{ log.rotate_count }}
    # Log files are rotated only if they grow bigger than log_rotate_size bytes. If size is followed by k, the size is assumed to be in kilobytes.
    # If the M is used, the size is in megabytes, and if G is used, the size is in gigabytes. So size 100, size 100k, size 100M and size 100G
    # are all valid.
    rotate_size: {{ log.rotate_size }}
    # The directory on your host that store log
    location: {{ log.location }}

    # Uncomment following lines to enable external syslog endpoint.
    # external_endpoint:
    #   # protocol used to transmit log to external endpoint, options is tcp or udp
    #   protocol: tcp
    #   # The host of external endpoint
    #   host: localhost
    #   # Port of external endpoint
    #   port: 5140

#This attribute is for migrator to detect the version of the .cfg file, DO NOT MODIFY!
_version: 1.9.0

{% if external_database is defined %}
# Uncomment external_database if using external database.
external_database:
  harbor:
    host: {{ external_database.harbor.host }}
    port: {{ external_database.harbor.port }}
    db_name: {{ external_database.harbor.db_name }}
    username: {{ external_database.harbor.username }}
    password: {{ external_database.harbor.password }}
    ssl_mode: {{ external_database.harbor.ssl_mode }}
    max_idle_conns: 50
    max_open_conns: 100
  clair:
    host: {{ external_database.clair.host }}
    port: {{ external_database.clair.port }}
    db_name: {{ external_database.clair.db_name }}
    username: {{ external_database.clair.username }}
    password: {{ external_database.clair.password }}
    ssl_mode: {{ external_database.clair.ssl_mode }}
  notary_signer:
    host: {{ external_database.notary_signer.host }}
    port: {{ external_database.notary_signer.port }}
    db_name: {{external_database.notary_signer.db_name }}
    username: {{ external_database.notary_signer.username }}
    password: {{ external_database.notary_signer.password }}
    ssl_mode: {{ external_database.notary_signer.ssl_mode }}
  notary_server:
    host: {{ external_database.notary_server.host }}
    port: {{ external_database.notary_server.port }}
    db_name: {{ external_database.notary_server.db_name }}
    username: {{ external_database.notary_server.username }}
    password: {{ external_database.notary_server.password }}
    ssl_mode: {{ external_database.notary_server.ssl_mode }}
{% else %}
# Uncomment external_database if using external database.
# external_database:
#   harbor:
#     host: harbor_db_host
#     port: harbor_db_port
#     db_name: harbor_db_name
#     username: harbor_db_username
#     password: harbor_db_password
#     ssl_mode: disable
#     max_idle_conns: 2
#     max_open_conns: 0
#   clair:
#     host: clair_db_host
#     port: clair_db_port
#     db_name: clair_db_name
#     username: clair_db_username
#     password: clair_db_password
#     ssl_mode: disable
#   notary_signer:
#     host: notary_signer_db_host
#     port: notary_signer_db_port
#     db_name: notary_signer_db_name
#     username: notary_signer_db_username
#     password: notary_signer_db_password
#     ssl_mode: disable
#   notary_server:
#     host: notary_server_db_host
#     port: notary_server_db_port
#     db_name: notary_server_db_name
#     username: notary_server_db_username
#     password: notary_server_db_password
#     ssl_mode: disable
{% endif %}

{% if external_redis is defined %}
external_redis:
  host: {{ external_redis.host }}
  port: {{ external_redis.port }}
  password: {{ external_redis.password }}
  # db_index 0 is for core, it's unchangeable
  registry_db_index: {{ external_redis.registry_db_index }}
  jobservice_db_index: {{ external_redis.jobservice_db_index }}
{% else %}
# Umcomments external_redis if using external Redis server
# external_redis:
#   host: redis
#   port: 6379
#   password:
#   # db_index 0 is for core, it's unchangeable
#   registry_db_index: 1
#   jobservice_db_index: 2
{% endif %}

{% if uaa is defined %}
# Uncomment uaa for trusting the certificate of uaa instance that is hosted via self-signed cert.
uaa:
  ca_file: {{ uaa.ca_file }}
{% endif %}

# Global proxy
# Config http proxy for components, e.g. http://my.proxy.com:3128
# Components doesn't need to connect to each others via http proxy.
# Remove component from `components` array if want disable proxy
# for it. If you want use proxy for replication, MUST enable proxy
# for core and jobservice, and set `http_proxy` and `https_proxy`.
# Add domain to the `no_proxy` field, when you want disable proxy
# for some special registry.
{% if clair is defined and (clair.http_proxy or clair.https_proxy) %}
proxy:
  http_proxy: {{ clair.http_proxy or ''}}
  https_proxy: {{ clair.https_proxy or ''}}
  no_proxy: {{ clair.no_proxy or ''}}
  components:
    - clair
{% else %}
proxy:
  http_proxy:
  https_proxy:
  no_proxy:
  components:
    - core
    - jobservice
    - clair
{% endif %}

swagger: '2.0'
info:
  title: Harbor API
  description: These APIs provide services for manipulating Harbor project.
  version: '2.0'
host: localhost
schemes:
  - http
  - https
basePath: /api/v2.0
produces:
  - application/json
consumes:
  - application/json
securityDefinitions:
  basic:
    type: basic
security:
  - basic: []
  - {}
paths:
  /health:
    get:
      summary: Check the status of Harbor components
      description: Check the status of Harbor components. This path does not require authentication.
      tags:
        - health
      operationId: getHealth
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: The health status of Harbor components
          schema:
            $ref: '#/definitions/OverallHealthStatus'
        '500':
          $ref: '#/responses/500'
  /search:
    get:
      summary: 'Search for projects and repositories'
      description: |-
        The Search endpoint returns information about the projects and repositories offered at public status or related to the current logged in user. The response includes the project and repository list in a proper display order.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: q
          in: query
          description: Search parameter for project and repository name.
          required: true
          type: string
      tags:
        - search
      operationId: search
      responses:
        '200':
          description: An array of search results
          schema:
            $ref: '#/definitions/Search'
        '500':
          $ref: '#/responses/500'
  /statistics:
    get:
      summary: Get the statistic information about the projects and repositories
      description: Get the statistic information about the projects and repositories
      tags:
        - statistic
      operationId: getStatistic
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: The statistic information
          schema:
            $ref: '#/definitions/Statistic'
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
  /ldap/ping:
    post:
      operationId: pingLdap
      summary: Ping available ldap service.
      description: |
        This endpoint ping the available ldap service for test related configuration parameters.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: ldapconf
          in: body
          description: 'ldap configuration. support input ldap service configuration. If it is a empty request, will load current configuration from the system.'
          required: false
          schema:
            $ref: '#/definitions/LdapConf'
      tags:
        - Ldap
      responses:
        '200':
          description: Ping ldap service successfully.
          schema:
            $ref: '#/definitions/LdapPingResult'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /ldap/users/search:
    get:
      operationId: searchLdapUser
      summary: Search available ldap users.
      description: |
        This endpoint searches the available ldap users based on related configuration parameters. Support searched by input ldap configuration, load configuration from the system and specific filter.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: username
          in: query
          type: string
          required: false
          description: Registered user ID
      tags:
        - Ldap
      responses:
        '200':
          description: Search ldap users successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/LdapUser'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /ldap/users/import:
    post:
      operationId: importLdapUser
      summary: Import selected available ldap users.
      description: |
        This endpoint adds the selected available ldap users to harbor based on related configuration parameters from the system. System will try to guess the user email address and realname, add to harbor user information. If have errors when import user, will return the list of importing failed uid and the failed reason.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: uid_list
          in: body
          description: The uid listed for importing. This list will check users validity of ldap service based on configuration from the system.
          required: true
          schema:
            $ref: '#/definitions/LdapImportUsers'
      tags:
        - Ldap
      responses:
        '200':
          description: Add ldap users successfully.
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          description: Failed import some users.
          schema:
            type: array
            items:
              $ref: '#/definitions/LdapFailedImportUser'
        '500':
          $ref: '#/responses/500'
  /ldap/groups/search:
    get:
      summary: Search available ldap groups.
      operationId: searchLdapGroup
      description: |
        This endpoint searches the available ldap groups based on related configuration parameters. support to search by groupname or groupdn.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: groupname
          in: query
          type: string
          required: false
          description: Ldap group name
        - name: groupdn
          in: query
          type: string
          required: false
          description: The LDAP group DN
      tags:
        - Ldap
      responses:
        '200':
          description: Search ldap group successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/UserGroup'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /internalconfig:
    get:
      summary: Get internal configurations.
      operationId: getInternalconfig
      description: |
        This endpoint is for retrieving system configurations that only provides for internal api call.
      tags:
        - configure
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get system configurations successfully. The response body is a map.
          schema:
            $ref: '#/definitions/InternalConfigurationsResponse'
        '401':
          description: User need to log in first.
        '403':
          description: User does not have permission of admin role.
        '500':
          description: Unexpected internal errors.
  /configurations:
    get:
      summary: Get system configurations.
      operationId: getConfigurations
      description: |
        This endpoint is for retrieving system configurations that only provides for admin user.
      tags:
        - configure
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get system configurations successfully. The response body is a map.
          schema:
            $ref: '#/definitions/ConfigurationsResponse'
        '401':
          description: User need to log in first.
        '403':
          description: User does not have permission of admin role.
        '500':
          description: Unexpected internal errors.
    put:
      summary: Modify system configurations.
      operationId: updateConfigurations
      description: |
        This endpoint is for modifying system configurations that only provides for admin user.
      tags:
        - configure
      parameters:
        - $ref: '#/parameters/requestId'
        - name: configurations
          in: body
          required: true
          schema:
            $ref: '#/definitions/Configurations'
          description: 'The configuration map can contain a subset of the attributes of the schema, which are to be updated.'
      responses:
        '200':
          description: Modify system configurations successfully.
        '401':
          description: User need to log in first.
        '403':
          description: User does not have permission of admin role.
        '500':
          description: Unexpected internal errors.
  /projects:
    get:
      summary: List projects
      description: This endpoint returns projects created by Harbor.
      tags:
        - project
      operationId: listProjects
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/sort'
        - name: name
          in: query
          description: The name of project.
          required: false
          type: string
        - name: public
          in: query
          description: The project is public or private.
          required: false
          type: boolean
        - name: owner
          in: query
          description: The name of project owner.
          required: false
          type: string
        - name: with_detail
          in: query
          description: Bool value indicating whether return detailed information of the project
          type: boolean
          required: false
          default: true
      responses:
        '200':
          description: Return all matched projects.
          schema:
            type: array
            items:
              $ref: '#/definitions/Project'
          headers:
            X-Total-Count:
              description: The total count of projects
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
    head:
      summary: Check if the project name user provided already exists.
      description: This endpoint is used to check if the project name provided already exist.
      tags:
        - project
      operationId: headProject
      parameters:
        - $ref: '#/parameters/requestId'
        - name: project_name
          in: query
          description: Project name for checking exists.
          required: true
          type: string
      responses:
        '200':
          $ref: '#/responses/200'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create a new project.
      description: This endpoint is for user to create a new project.
      tags:
        - project
      operationId: createProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/resourceNameInLocation'
        - name: project
          in: body
          description: New created project.
          required: true
          schema:
            $ref: '#/definitions/ProjectReq'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}':
    get:
      summary: Return specific project detail information
      description: This endpoint returns specific project information by project ID.
      tags:
        - project
      operationId: getProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
      responses:
        '200':
          description: Return matched project information.
          schema:
            $ref: '#/definitions/Project'
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update properties for a selected project.
      description: This endpoint is aimed to update the properties of a project.
      tags:
        - project
      operationId: updateProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: project
          in: body
          required: true
          schema:
            $ref: '#/definitions/ProjectReq'
          description: Updates of project.
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete project by projectID
      description: This endpoint is aimed to delete project by project ID.
      tags:
        - project
      operationId: deleteProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '412':
          $ref: '#/responses/412'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name_or_id}/_deletable:
    get:
      summary: Get the deletable status of the project
      description: Get the deletable status of the project
      tags:
        - project
      operationId: getProjectDeletable
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
      responses:
        '200':
          description: Return deletable status of the project.
          schema:
            $ref: '#/definitions/ProjectDeletable'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/summary':
    get:
      summary: Get summary of the project.
      description: Get summary of the project.
      tags:
        - project
      operationId: getProjectSummary
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
      responses:
        '200':
          description: Get summary of the project successfully.
          schema:
            $ref: '#/definitions/ProjectSummary'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/members':
    get:
      summary: Get all project member information
      description: Get all project member information
      operationId: listProjectMembers
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: entityname
          in: query
          type: string
          description: The entity name to search.
      tags:
        - member
      responses:
        '200':
          description: Get project members successfully.
          headers:
            X-Total-Count:
              description: The total count of members
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/ProjectMemberEntity'

        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create project member
      operationId: createProjectMember
      description: 'Create project member relationship, the member can be one of the user_member and group_member,  The user_member need to specify user_id or username. If the user already exist in harbor DB, specify the user_id,  If does not exist in harbor DB, it will SearchAndOnBoard the user. The group_member need to specify id or ldap_group_dn. If the group already exist in harbor DB. specify the user group''s id,  If does not exist, it will SearchAndOnBoard the group. '
      tags:
        - member
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: project_member
          in: body
          schema:
            $ref: '#/definitions/ProjectMember'
      responses:
        '201':
          description: Project member created successfully.
          headers:
            Location:
              type: string
              description: The URL of the created resource
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/members/{mid}':
    get:
      summary: Get the project member information
      description: Get the project member information
      operationId: getProjectMember
      tags:
        - member
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: mid
          in: path
          type: integer
          format: int64
          required: true
          description: The member ID
      responses:
        '200':
          description: Project member retrieved successfully.
          schema:
            $ref: '#/definitions/ProjectMemberEntity'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update project member
      description: Update project member relationship
      operationId: updateProjectMember
      tags:
        - member
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: mid
          in: path
          type: integer
          format: int64
          required: true
          description: Member ID.
        - name: role
          in: body
          schema:
            $ref: '#/definitions/RoleRequest'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete project member
      operationId: deleteProjectMember
      tags:
        - member
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: mid
          in: path
          type: integer
          format: int64
          required: true
          description: Member ID.
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/metadatas/':
    get:
      summary: Get the metadata of the specific project
      description: Get the metadata of the specific project
      operationId: listProjectMetadatas
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
      tags:
        - projectMetadata
      responses:
        '200':
          description: Success
          schema:
            type: object
            additionalProperties:
              type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Add metadata for the specific project
      operationId: addProjectMetadatas
      description: Add metadata for the specific project
      tags:
        - projectMetadata
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: metadata
          in: body
          schema:
            type: object
            additionalProperties:
              type: string
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/metadatas/{meta_name}':
    get:
      summary: Get the specific metadata of the specific project
      description: Get the specific metadata of the specific project
      operationId: getProjectMetadata
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: meta_name
          in: path
          description: The name of metadata.
          required: true
          type: string
      tags:
        - projectMetadata
      responses:
        '200':
          description: Success
          schema:
            type: object
            additionalProperties:
              type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update the specific metadata for the specific project
      description: Update the specific metadata for the specific project
      operationId: updateProjectMetadata
      tags:
        - projectMetadata
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: meta_name
          in: path
          description: The name of metadata.
          required: true
          type: string
        - name: metadata
          in: body
          schema:
            type: object
            additionalProperties:
              type: string
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete the specific metadata for the specific project
      description: Delete the specific metadata for the specific project
      operationId: deleteProjectMetadata
      tags:
        - projectMetadata
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: meta_name
          in: path
          description: The name of metadata.
          required: true
          type: string
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /repositories:
    get:
      summary: List all authorized repositories
      description: List all authorized repositories
      tags:
        - repository
      operationId: listAllRepositories
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of repositories
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Repository'
        '400':
          $ref: '#/responses/400'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories:
    get:
      summary: List repositories
      description: List repositories of the specified project
      tags:
        - repository
      operationId: listRepositories
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of repositories
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Repository'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}:
    get:
      summary: Get repository
      description: Get the repository specified by name
      tags:
        - repository
      operationId: getRepository
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/Repository'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update repository
      description: Update the repository specified by name
      tags:
        - repository
      operationId: updateRepository
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - name: repository
          in: body
          description: The JSON object of repository.
          required: true
          schema:
            $ref: '#/definitions/Repository'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete repository
      description: Delete the repository specified by name
      tags:
        - repository
      operationId: deleteRepository
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts:
    get:
      summary: List artifacts
      description: List artifacts under the specific project and repository. Except the basic properties, the other supported queries in "q" includes "tags=*" to list only tagged artifacts, "tags=nil" to list only untagged artifacts, "tags=~v" to list artifacts whose tag fuzzy matches "v", "tags=v" to list artifact whose tag exactly matches "v", "labels=(id1, id2)" to list artifacts that both labels with id1 and id2 are added to
      tags:
        - artifact
      operationId: listArtifacts
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/acceptVulnerabilities'
        - name: with_tag
          in: query
          description: Specify whether the tags are included inside the returning artifacts
          type: boolean
          required: false
          default: true
        - name: with_label
          in: query
          description: Specify whether the labels are included inside the returning artifacts
          type: boolean
          required: false
          default: false
        - name: with_scan_overview
          in: query
          description: Specify whether the scan overview is included inside the returning artifacts
          type: boolean
          required: false
          default: false
        - name: with_sbom_overview
          in: query
          description: Specify whether the SBOM overview is included in returning artifacts, when this option is true, the SBOM overview will be included in the response
          type: boolean
          required: false
          default: false
        - name: with_signature
          in: query
          description: Specify whether the signature is included inside the tags of the returning artifacts. Only works when setting "with_tag=true"
          type: boolean
          required: false
          default: false
        - name: with_immutable_status
          in: query
          description: Specify whether the immutable status is included inside the tags of the returning artifacts. Only works when setting "with_immutable_status=true"
          type: boolean
          required: false
          default: false
        - name: with_accessory
          in: query
          description: Specify whether the accessories are included of the returning artifacts. Only works when setting "with_accessory=true"
          type: boolean
          required: false
          default: false
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of artifacts
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Artifact'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Copy artifact
      description: Copy the artifact specified in the "from" parameter to the repository.
      tags:
        - artifact
      operationId: CopyArtifact
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - name: from
          in: query
          description: The artifact from which the new artifact is copied from, the format should be "project/repository:tag" or "project/repository@digest".
          type: string
          required: true
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '405':
          $ref: '#/responses/405'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}:
    get:
      summary: Get the specific artifact
      description: Get the artifact specified by the reference under the project and repository. The reference can be digest or tag.
      tags:
        - artifact
      operationId: getArtifact
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/acceptVulnerabilities'
        - name: with_tag
          in: query
          description: Specify whether the tags are inclued inside the returning artifacts
          type: boolean
          required: false
          default: true
        - name: with_label
          in: query
          description: Specify whether the labels are inclued inside the returning artifacts
          type: boolean
          required: false
          default: false
        - name: with_scan_overview
          in: query
          description: Specify whether the scan overview is inclued inside the returning artifacts
          type: boolean
          required: false
          default: false
        - name: with_sbom_overview
          in: query
          description: Specify whether the SBOM overview is included in returning artifact, when this option is true, the SBOM overview will be included in the response
          type: boolean
          required: false
          default: false
        - name: with_accessory
          in: query
          description: Specify whether the accessories are included of the returning artifacts.
          type: boolean
          required: false
          default: false
        # should be in tag level
        - name: with_signature
          in: query
          description: Specify whether the signature is inclued inside the returning artifacts
          type: boolean
          required: false
          default: false
        - name: with_immutable_status
          in: query
          description: Specify whether the immutable status is inclued inside the tags of the returning artifacts.
          type: boolean
          required: false
          default: false
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/Artifact'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete the specific artifact
      description: Delete the artifact specified by the reference under the project and repository. The reference can be digest or tag
      tags:
        - artifact
      operationId: deleteArtifact
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/scan:
    post:
      summary: Scan the artifact
      description: Scan the specified artifact
      tags:
        - scan
      operationId: scanArtifact
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - name: scanType
          in: body
          required: false
          schema:
            $ref: '#/definitions/ScanType'
      responses:
        '202':
          $ref: '#/responses/202'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '422':
          $ref: '#/responses/422'  
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/scan/stop:
    post:
      summary: Cancelling a scan job for a particular artifact
      description: Cancelling a scan job for a particular artifact
      tags:
        - scan
      operationId: stopScanArtifact
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - name: scanType
          in: body
          required: true
          schema:
            $ref: '#/definitions/ScanType'
          description: 'The scan type: Vulnerabilities, SBOM'
      responses:
        '202':
          $ref: '#/responses/202'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '422':
          $ref: '#/responses/422'  
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/scan/{report_id}/log:
    get:
      summary: Get the log of the scan report
      description: Get the log of the scan report
      tags:
        - scan
      operationId: getReportLog
      produces:
        - text/plain
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - name: report_id
          type: string
          in: path
          required: true
          description: The report id to get the log
      responses:
        '200':
          description: Successfully get scan log file
          schema:
            type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/tags:
    post:
      summary: Create tag
      description: Create a tag for the specified artifact
      tags:
        - artifact
      operationId: createTag
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - name: tag
          in: body
          description: The JSON object of tag.
          required: true
          schema:
            $ref: '#/definitions/Tag'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '405':
          $ref: '#/responses/405'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
    get:
      summary: List tags
      description: List tags of the specific artifact
      tags:
        - artifact
      operationId: listTags
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: with_signature
          in: query
          description: Specify whether the signature is included inside the returning tags
          type: boolean
          required: false
          default: false
        - name: with_immutable_status
          in: query
          description: Specify whether the immutable status is included inside the returning tags
          type: boolean
          required: false
          default: false
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of tags
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Tag'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/tags/{tag_name}:
    delete:
      summary: Delete tag
      description: Delete the tag of the specified artifact
      tags:
        - artifact
      operationId: deleteTag
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - $ref: '#/parameters/tagName'
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/accessories:
    get:
      summary: List accessories
      description: List accessories of the specific artifact
      tags:
        - artifact
      operationId: listAccessories
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of accessories
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Accessory'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/additions/vulnerabilities:
    get:
      summary: Get the vulnerabilities addition of the specific artifact
      description: Get the vulnerabilities addition of the artifact specified by the reference under the project and repository.
      tags:
        - artifact
      operationId: getVulnerabilitiesAddition
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - $ref: '#/parameters/acceptVulnerabilities'
      responses:
        '200':
          description: Success
          headers:
            Content-Type:
              description: The content type of the vulnerabilities addition
              type: string
          schema:
            type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/additions/{addition}:
    get:
      summary: Get the addition of the specific artifact
      description: Get the addition of the artifact specified by the reference under the project and repository.
      tags:
        - artifact
      operationId: getAddition
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - name: addition
          in: path
          description: The type of addition.
          type: string
          enum: [build_history, values.yaml, readme.md, dependencies, sbom]
          required: true
      responses:
        '200':
          description: Success
          headers:
            Content-Type:
              description: The content type of the addition
              type: string
          schema:
            type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '422':
          $ref: '#/responses/422'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/labels:
    post:
      summary: Add label to artifact
      description: Add label to the specified artiact.
      tags:
        - artifact
      operationId: addLabel
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - name: label
          in: body
          description: The label that added to the artifact. Only the ID property is needed.
          required: true
          schema:
            $ref: '#/definitions/Label'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/repositories/{repository_name}/artifacts/{reference}/labels/{label_id}:
    delete:
      summary: Remove label from artifact
      description: Remove the label from the specified artiact.
      tags:
        - artifact
      operationId: removeLabel
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/repositoryName'
        - $ref: '#/parameters/reference'
        - name: label_id
          in: path
          description: The ID of the label that removed from the artifact.
          type: integer
          format: int64
          required: true
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name_or_id}/artifacts:
    get:
      summary: List artifacts
      description: List artifacts of the specified project
      tags:
        - project
      operationId: listArtifactsOfProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/acceptVulnerabilities'
        - name: with_tag
          in: query
          description: Specify whether the tags are included inside the returning artifacts
          type: boolean
          required: false
          default: true
        - name: with_label
          in: query
          description: Specify whether the labels are included inside the returning artifacts
          type: boolean
          required: false
          default: false
        - name: with_scan_overview
          in: query
          description: Specify whether the scan overview is included inside the returning artifacts
          type: boolean
          required: false
          default: false
        - name: with_sbom_overview
          in: query
          description: Specify whether the SBOM overview is included in returning artifacts, when this option is true, the SBOM overview will be included in the response
          type: boolean
          required: false
          default: false
        - name: with_immutable_status
          in: query
          description: Specify whether the immutable status is included inside the tags of the returning artifacts. Only works when setting "with_immutable_status=true"
          type: boolean
          required: false
          default: false
        - name: with_accessory
          in: query
          description: Specify whether the accessories are included of the returning artifacts. Only works when setting "with_accessory=true"
          type: boolean
          required: false
          default: false
        - name: latest_in_repository
          in: query
          description: Specify whether only the latest pushed artifact of each repository is included inside the returning artifacts. Only works when either artifact_type or media_type is included in the query.
          type: boolean
          required: false
          default: false
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of artifacts
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Artifact'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/scanner':
    get:
      summary: Get project level scanner
      description: Get the scanner registration of the specified project. If no scanner registration is configured for the specified project, the system default scanner registration will be returned.
      tags:
        - project
      operationId: getScannerOfProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
      responses:
        '200':
          description: The details of the scanner registration.
          schema:
            $ref: '#/definitions/ScannerRegistration'
        '400':
          description: Bad project ID
        '401':
          description: Unauthorized request
        '403':
          description: Request is not allowed
        '404':
          description: The requested object is not found
        '500':
          description: Internal server error happened
    put:
      summary: Configure scanner for the specified project
      description: Set one of the system configured scanner registration as the indepndent scanner of the specified project.
      tags:
        - project
      operationId: setScannerOfProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: payload
          in: body
          required: true
          schema:
            $ref: '#/definitions/ProjectScanner'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/scanner/candidates':
    get:
      summary: Get scanner registration candidates for configurating project level scanner
      description: Retrieve the system configured scanner registrations as candidates of setting project level scanner.
      tags:
        - project
      operationId: listScannerCandidatesOfProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: A list of scanner registrations.
          schema:
            type: array
            items:
              $ref: '#/definitions/ScannerRegistration'
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /audit-logs:
    get:
      summary: Get recent logs of the projects which the user is a member of
      description: |
        This endpoint let user see the recent operation logs of the projects which he is member of
      tags:
        - auditlog
      operationId: listAuditLogs
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of auditlogs
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/AuditLog'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/logs:
    get:
      summary: Get recent logs of the projects
      description: Get recent logs of the projects
      tags:
        - project
      operationId: getLogs
      parameters:
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of auditlogs
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/AuditLog'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
  /p2p/preheat/providers:
    get:
      summary: List P2P providers
      description: List P2P providers
      tags:
        - preheat
      operationId: ListProviders
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/Metadata'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /p2p/preheat/instances/ping:
    post:
      summary: Ping status of a instance.
      description: |
        This endpoint checks status of a instance, the instance can be given by ID or Endpoint URL (together with credential)
      operationId: PingInstances
      parameters:
        - $ref: '#/parameters/requestId'
        - name: instance
          in: body
          description: The JSON object of instance.
          required: true
          schema:
            $ref: '#/definitions/Instance'
      tags:
        - preheat
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          description: Instance not found (when instance is provided by ID).
        '500':
          $ref: '#/responses/500'
  /p2p/preheat/instances:
    get:
      summary: List P2P provider instances
      description: List P2P provider instances
      tags:
        - preheat
      operationId: ListInstances
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of preheating provider instances
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Instance'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create p2p provider instances
      description: Create p2p provider instances
      tags:
        - preheat
      operationId: CreateInstance
      parameters:
        - $ref: '#/parameters/requestId'
        - name: instance
          in: body
          description: The JSON object of instance.
          required: true
          schema:
            $ref: '#/definitions/Instance'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /p2p/preheat/instances/{preheat_instance_name}:
    get:
      summary: Get a P2P provider instance
      description: Get a P2P provider instance
      tags:
        - preheat
      operationId: GetInstance
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/instanceName'
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/Instance'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete the specified P2P provider instance
      description: Delete the specified P2P provider instance
      tags:
        - preheat
      operationId: DeleteInstance
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/instanceName'
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update the specified P2P provider instance
      description: Update the specified P2P provider instance
      tags:
        - preheat
      operationId: UpdateInstance
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/instanceName'
        - name: instance
          in: body
          description: The instance to update
          required: true
          schema:
            $ref: '#/definitions/Instance'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/preheat/policies:
    post:
      summary: Create a preheat policy under a project
      description: Create a preheat policy under a project
      tags:
        - preheat
      operationId: CreatePolicy
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - name: policy
          in: body
          description: The policy schema info
          required: true
          schema:
            $ref: '#/definitions/PreheatPolicy'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
    get:
      summary: List preheat policies
      description: List preheat policies
      tags:
        - preheat
      operationId: ListPolicies
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
      responses:
        '200':
          description: List preheat policies success
          headers:
            X-Total-Count:
              description: The total count of policies
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/PreheatPolicy'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/preheat/policies/{preheat_policy_name}:
    get:
      summary: Get a preheat policy
      description: Get a preheat policy
      tags:
        - preheat
      operationId: GetPolicy
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/preheatPolicyName'
      responses:
        '200':
          description: Get a preheat policy success
          schema:
            $ref: '#/definitions/PreheatPolicy'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update preheat policy
      description: Update preheat policy
      tags:
        - preheat
      operationId: UpdatePolicy
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/preheatPolicyName'
        - name: policy
          in: body
          description: The policy schema info
          required: true
          schema:
            $ref: '#/definitions/PreheatPolicy'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Manual preheat
      description: Manual preheat
      tags:
        - preheat
      operationId: ManualPreheat
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/preheatPolicyName'
        - name: policy
          in: body
          description: The policy schema info
          required: true
          schema:
            $ref: '#/definitions/PreheatPolicy'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete a preheat policy
      description: Delete a preheat policy
      tags:
        - preheat
      operationId: DeletePolicy
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/preheatPolicyName'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/preheat/policies/{preheat_policy_name}/executions:
    get:
      summary: List executions for the given policy
      description: List executions for the given policy
      tags:
        - preheat
      operationId: ListExecutions
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/preheatPolicyName'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
      responses:
        '200':
          description: List executions success
          headers:
            X-Total-Count:
              description: The total count of executions
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Execution'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/preheat/policies/{preheat_policy_name}/executions/{execution_id}:
    get:
      summary: Get a execution detail by id
      description: Get a execution detail by id
      tags:
        - preheat
      operationId: GetExecution
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/preheatPolicyName'
        - $ref: '#/parameters/executionId'
      responses:
        '200':
          description: Get execution success
          schema:
            $ref: '#/definitions/Execution'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    patch:
      summary: Stop a execution
      description: Stop a execution
      tags:
        - preheat
      operationId: StopExecution
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/preheatPolicyName'
        - $ref: '#/parameters/executionId'
        - name: execution
          description: The data of execution
          in: body
          required: true
          schema:
            $ref: '#/definitions/Execution'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/preheat/policies/{preheat_policy_name}/executions/{execution_id}/tasks:
    get:
      summary: List all the related tasks for the given execution
      description: List all the related tasks for the given execution
      tags:
        - preheat
      operationId: ListTasks
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/preheatPolicyName'
        - $ref: '#/parameters/executionId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
      responses:
        '200':
          description: List tasks success
          headers:
            X-Total-Count:
              description: The total count of tasks
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Task'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/preheat/policies/{preheat_policy_name}/executions/{execution_id}/tasks/{task_id}/logs:
    get:
      summary: Get the log text stream of the specified task for the given execution
      description: Get the log text stream of the specified task for the given execution
      tags:
        - preheat
      operationId: GetPreheatLog
      produces:
        - text/plain
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
        - $ref: '#/parameters/preheatPolicyName'
        - $ref: '#/parameters/executionId'
        - $ref: '#/parameters/taskId'
      responses:
        '200':
          description: Get log success
          headers:
            Content-Type:
              description: Content type of response
              type: string
          schema:
            type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name}/preheat/providers:
    get:
      summary: Get all providers at project level
      description: Get all providers at project level
      tags:
        - preheat
      operationId: ListProvidersUnderProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/projectName'
      responses:
        '200':
          description: Success
          schema:
            type: array
            items:
              $ref: '#/definitions/ProviderUnderProject'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name_or_id}/robots:
    get:
      summary: Get all robot accounts of specified project
      description: Get all robot accounts of specified project
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
      tags:
        - robotv1
      operationId: ListRobotV1
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of robot accounts
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Robot'
        '400':
          $ref: '#/responses/400'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create a robot account
      description: Create a robot account
      tags:
        - robotv1
      operationId: CreateRobotV1
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: robot
          in: body
          description: The JSON object of a robot account.
          required: true
          schema:
            $ref: '#/definitions/RobotCreateV1'
      responses:
        '201':
          description: Created
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
            Location:
              description: The location of the resource
              type: string
          schema:
            $ref: '#/definitions/RobotCreated'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /projects/{project_name_or_id}/robots/{robot_id}:
    get:
      summary: Get a robot account
      description: This endpoint returns specific robot account information by robot ID.
      tags:
        - robotv1
      operationId: GetRobotByIDV1
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/robotId'
      responses:
        '200':
          description: Return matched robot information.
          schema:
            $ref: '#/definitions/Robot'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update status of robot account.
      description: Used to disable/enable a specified robot account.
      tags:
        - robotv1
      operationId: UpdateRobotV1
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/robotId'
        - name: robot
          in: body
          description: The JSON object of a robot account.
          required: true
          schema:
            $ref: '#/definitions/Robot'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete a robot account
      description: This endpoint deletes specific robot account information by robot ID.
      tags:
        - robotv1
      operationId: DeleteRobotV1
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/robotId'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/immutabletagrules':
    get:
      summary: List all immutable tag rules of current project
      description: |
        This endpoint returns the immutable tag rules of a project
      tags:
        - immutable
      operationId: ListImmuRules
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of immutable tag
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/ImmutableRule'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Add an immutable tag rule to current project
      description: |
        This endpoint add an immutable tag rule to the project
      tags:
        - immutable
      operationId: CreateImmuRule
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: ImmutableRule
          in: body
          required: true
          schema:
            $ref: '#/definitions/ImmutableRule'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/immutabletagrules/{immutable_rule_id}':
    put:
      summary: Update the immutable tag rule or enable or disable the rule
      tags:
        - immutable
      operationId: UpdateImmuRule
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/immutableRuleId'
        - name: ImmutableRule
          in: body
          required: true
          schema:
            $ref: '#/definitions/ImmutableRule'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete the immutable tag rule.
      tags:
        - immutable
      operationId: DeleteImmuRule
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/immutableRuleId'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/webhook/policies':
    get:
      summary: List project webhook policies.
      description: |
        This endpoint returns webhook policies of a project.
      tags:
        - webhook
      operationId: ListWebhookPoliciesOfProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of webhook policies.
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/WebhookPolicy'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create project webhook policy.
      description: |
        This endpoint create a webhook policy if the project does not have one.
      tags:
        - webhook
      operationId: CreateWebhookPolicyOfProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - name: policy
          in: body
          description: Properties "targets" and "event_types" needed.
          required: true
          schema:
            $ref: '#/definitions/WebhookPolicy'
      responses:
        '201':
          description: Project webhook policy create successfully.
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
            Location:
              description: The location of the resource
              type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/webhook/policies/{webhook_policy_id}':
    get:
      summary: Get project webhook policy
      description: |
        This endpoint returns specified webhook policy of a project.
      tags:
        - webhook
      operationId: GetWebhookPolicyOfProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/webhookPolicyId'
      responses:
        '200':
          description: Get webhook policy successfully.
          schema:
            $ref: '#/definitions/WebhookPolicy'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update webhook policy of a project.
      description: |
        This endpoint is aimed to update the webhook policy of a project.
      tags:
        - webhook
      operationId: UpdateWebhookPolicyOfProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/webhookPolicyId'
        - name: policy
          in: body
          description: All properties needed except "id", "project_id", "creation_time", "update_time".
          required: true
          schema:
            $ref: '#/definitions/WebhookPolicy'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete webhook policy of a project
      description: |
        This endpoint is aimed to delete webhookpolicy of a project.
      tags:
        - webhook
      operationId: DeleteWebhookPolicyOfProject
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/webhookPolicyId'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/webhook/policies/{webhook_policy_id}/executions':
    get:
      summary: List executions for a specific webhook policy
      description: |
        This endpoint returns the executions of a specific webhook policy.
      tags:
        - webhook
      operationId: ListExecutionsOfWebhookPolicy
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/webhookPolicyId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
      responses:
        '200':
          description: List webhook executions success
          headers:
            X-Total-Count:
              description: The total count of executions
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Execution'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/webhook/policies/{webhook_policy_id}/executions/{execution_id}/tasks':
    get:
      summary: List tasks for a specific webhook execution
      description: |
        This endpoint returns the tasks of a specific webhook execution.
      tags:
        - webhook
      operationId: ListTasksOfWebhookExecution
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/webhookPolicyId'
        - $ref: '#/parameters/executionId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
      responses:
        '200':
          description: List tasks of webhook executions success
          headers:
            X-Total-Count:
              description: The total count of tasks
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Task'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/webhook/policies/{webhook_policy_id}/executions/{execution_id}/tasks/{task_id}/log':
    get:
      summary: Get logs for a specific webhook task
      description: |
        This endpoint returns the logs of a specific webhook task.
      tags:
        - webhook
      operationId: GetLogsOfWebhookTask
      produces:
        - text/plain
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/webhookPolicyId'
        - $ref: '#/parameters/executionId'
        - $ref: '#/parameters/taskId'
      responses:
        '200':
          description: Get log success
          headers:
            Content-Type:
              description: Content type of response
              type: string
          schema:
            type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/webhook/lasttrigger':
    get:
      deprecated: true
      summary: Get project webhook policy last trigger info
      description: |
        This endpoint returns last trigger information of project webhook policy.
      tags:
        - webhook
      operationId: LastTrigger
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
      responses:
        '200':
          description: Test webhook connection successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/WebhookLastTrigger'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/webhook/jobs':
    get:
      deprecated: true
      summary: List project webhook jobs
      description: |
        This endpoint returns webhook jobs of a project.
      tags:
        - webhookjob
      operationId: ListWebhookJobs
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: policy_id
          in: query
          type: integer
          format: int64
          required: true
          description: The policy ID.
        - name: status
          in: query
          description: The status of webhook job.
          required: false
          type: array
          items:
            type: string
      responses:
        '200':
          description: List project webhook jobs successfully.
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/WebhookJob'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/projects/{project_name_or_id}/webhook/events':
    get:
      summary: Get supported event types and notify types.
      description: Get supported event types and notify types.
      tags:
        - webhook
      operationId: GetSupportedEventTypes
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/isResourceName'
        - $ref: '#/parameters/projectNameOrId'
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/SupportedWebhookEventTypes'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /usergroups:
    get:
      summary: Get all user groups information
      description: Get all user groups information, it is open for system admin
      operationId: listUserGroups
      tags:
        - usergroup
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: ldap_group_dn
          in: query
          type: string
          required: false
          description: search with ldap group DN
        - name: group_name
          in: query
          type: string
          required: false
          description: group name need to search, fuzzy matches
      responses:
        '200':
          description: Get user group successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/UserGroup'
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create user group
      description: Create user group information
      operationId: createUserGroup
      tags:
        - usergroup
      parameters:
        - $ref: '#/parameters/requestId'
        - name: usergroup
          in: body
          schema:
            $ref: '#/definitions/UserGroup'
      responses:
        '201':
          description: User group created successfully.
          headers:
            Location:
              type: string
              description: The URL of the created resource
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /usergroups/search:
    get:
      summary: Search groups by groupname
      description: |
        This endpoint is to search groups by group name.  It's open for all authenticated requests.
      tags:
        - usergroup
      operationId: searchUserGroups
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: groupname
          in: query
          type: string
          required: true
          description: Group name for filtering results.
      responses:
        '200':
          description: Search groups successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/UserGroupSearchItem'
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
  '/usergroups/{group_id}':
    get:
      summary: Get user group information
      description: Get user group information
      operationId: getUserGroup
      tags:
        - usergroup
      parameters:
        - $ref: '#/parameters/requestId'
        - name: group_id
          in: path
          type: integer
          format: int64
          required: true
          description: Group ID
      responses:
        '200':
          description: User group get successfully.
          schema:
            $ref: '#/definitions/UserGroup'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update group information
      description: Update user group information
      operationId: updateUserGroup
      tags:
        - usergroup
      parameters:
        - $ref: '#/parameters/requestId'
        - name: group_id
          in: path
          type: integer
          format: int64
          required: true
          description: Group ID
        - name: usergroup
          in: body
          required: false
          schema:
            $ref: '#/definitions/UserGroup'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete user group
      description: Delete user group
      operationId: deleteUserGroup
      tags:
        - usergroup
      parameters:
        - $ref: '#/parameters/requestId'
        - name: group_id
          type: integer
          in: path
          required: true
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /icons/{digest}:
    get:
      summary: Get artifact icon
      description: Get the artifact icon with the specified digest. As the original icon image is resized and encoded before returning, the parameter "digest" in the path doesn't match the hash of the returned content
      tags:
        - icon
      operationId: getIcon
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/digest'
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/Icon'
        '400':
          $ref: '#/responses/400'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /robots:
    get:
      summary: Get robot account
      description: List the robot accounts with the specified level and project.
      tags:
        - robot
      operationId: ListRobot
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of robot accounts
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Robot'
        '400':
          $ref: '#/responses/400'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create a robot account
      description: Create a robot account
      tags:
        - robot
      operationId: CreateRobot
      parameters:
        - $ref: '#/parameters/requestId'
        - name: robot
          in: body
          description: The JSON object of a robot account.
          required: true
          schema:
            $ref: '#/definitions/RobotCreate'
      responses:
        '201':
          description: Created
          headers:
            X-Request-Id:
              description: The ID of the corresponding request for the response
              type: string
            Location:
              description: The location of the resource
              type: string
          schema:
            $ref: '#/definitions/RobotCreated'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  '/quotas':
    get:
      summary: List quotas
      description: List quotas
      tags:
        - quota
      operationId: listQuotas
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: reference
          in: query
          description: The reference type of quota.
          required: false
          type: string
        - name: reference_id
          in: query
          description: The reference id of quota.
          required: false
          type: string
        - name: sort
          in: query
          type: string
          required: false
          description: |
            Sort method, valid values include:
            'hard.resource_name', '-hard.resource_name', 'used.resource_name', '-used.resource_name'.
            Here '-' stands for descending order, resource_name should be the real resource name of the quota.
      responses:
        '200':
          description: Successfully retrieved the quotas.
          schema:
            type: array
            items:
              $ref: '#/definitions/Quota'
          headers:
            X-Total-Count:
              description: The total count of access logs
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/quotas/{id}':
    get:
      summary: Get the specified quota
      description: Get the specified quota
      tags:
        - quota
      operationId: getQuota
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          required: true
          description: Quota ID
      responses:
        '200':
          description: Successfully retrieved the quota.
          schema:
            $ref: '#/definitions/Quota'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update the specified quota
      description: Update hard limits of the specified quota
      tags:
        - quota
      operationId: updateQuota
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          required: true
          description: Quota ID
        - name: hard
          in: body
          required: true
          description: The new hard limits for the quota
          schema:
            $ref: '#/definitions/QuotaUpdateReq'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /robots/{robot_id}:
    get:
      summary: Get a robot account
      description: This endpoint returns specific robot account information by robot ID.
      tags:
        - robot
      operationId: GetRobotByID
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/robotId'
      responses:
        '200':
          description: Return matched robot information.
          schema:
            $ref: '#/definitions/Robot'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update a robot account
      description: This endpoint updates specific robot account information by robot ID.
      tags:
        - robot
      operationId: UpdateRobot
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/robotId'
        - name: robot
          in: body
          description: The JSON object of a robot account.
          required: true
          schema:
            $ref: '#/definitions/Robot'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
    patch:
      summary: Refresh the robot secret
      description: Refresh the robot secret
      tags:
        - robot
      operationId: RefreshSec
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/robotId'
        - name: robotSec
          description: The JSON object of a robot account.
          in: body
          required: true
          schema:
            $ref: '#/definitions/RobotSec'
      responses:
        '200':
          description: Return refreshed robot sec.
          schema:
            $ref: '#/definitions/RobotSec'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete a robot account
      description: This endpoint deletes specific robot account information by robot ID.
      tags:
        - robot
      operationId: DeleteRobot
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/robotId'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /replication/policies:
    get:
      summary: List replication policies
      description: List replication policies
      tags:
        - replication
      operationId: listReplicationPolicies
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: name
          in: query
          type: string
          required: false
          description: Deprecated, use "query" instead. The policy name.
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of the resources
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/ReplicationPolicy'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create a replication policy
      description: Create a replication policy
      tags:
        - replication
      operationId: createReplicationPolicy
      parameters:
        - $ref: '#/parameters/requestId'
        - name: policy
          in: body
          description: The replication policy
          required: true
          schema:
            $ref: '#/definitions/ReplicationPolicy'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /replication/policies/{id}:
    get:
      summary: Get the specific replication policy
      description: Get the specific replication policy
      tags:
        - replication
      operationId: getReplicationPolicy
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Policy ID
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/ReplicationPolicy'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete the specific replication policy
      description: Delete the specific replication policy
      tags:
        - replication
      operationId: deleteReplicationPolicy
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Replication policy ID
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '412':
          $ref: '#/responses/412'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update the replication policy
      description: Update the replication policy
      tags:
        - replication
      operationId: updateReplicationPolicy
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: The policy ID
        - name: policy
          in: body
          description: The replication policy
          required: true
          schema:
            $ref: '#/definitions/ReplicationPolicy'
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /replication/executions:
    get:
      summary: List replication executions
      description: List replication executions
      tags:
        - replication
      operationId: listReplicationExecutions
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: policy_id
          in: query
          type: integer
          required: false
          description: The ID of the policy that the executions belong to.
        - name: status
          in: query
          type: string
          required: false
          description: The execution status.
        - name: trigger
          in: query
          type: string
          required: false
          description: The trigger mode.
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of the resources
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/ReplicationExecution'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Start one replication execution
      description: Start one replication execution according to the policy
      tags:
        - replication
      operationId: startReplication
      parameters:
        - $ref: '#/parameters/requestId'
        - name: execution
          in: body
          description: The ID of policy that the execution belongs to
          required: true
          schema:
            $ref: '#/definitions/StartReplicationExecution'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /replication/executions/{id}:
    get:
      summary: Get the specific replication execution
      description: Get the replication execution specified by ID
      tags:
        - replication
      operationId: getReplicationExecution
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          description: The ID of the execution.
          required: true
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/ReplicationExecution'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Stop the specific replication execution
      description: Stop the replication execution specified by ID
      tags:
        - replication
      operationId: stopReplication
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          description: The ID of the execution.
          required: true
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /replication/executions/{id}/tasks:
    get:
      summary: List replication tasks for a specific execution
      description: List replication tasks for a specific execution
      tags:
        - replication
      operationId: listReplicationTasks
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: id
          in: path
          type: integer
          format: int64
          description: The ID of the execution that the tasks belongs to.
          required: true
        - name: status
          in: query
          type: string
          required: false
          description: The task status.
        - name: resource_type
          in: query
          type: string
          required: false
          description: The resource type.
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of the resources
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/ReplicationTask'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /replication/executions/{id}/tasks/{task_id}/log:
    get:
      summary: Get the log of the specific replication task
      description: Get the log of the specific replication task
      tags:
        - replication
      operationId: getReplicationLog
      produces:
        - text/plain
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          description: The ID of the execution that the tasks belongs to.
          required: true
        - name: task_id
          in: path
          type: integer
          format: int64
          description: The ID of the task.
          required: true
      responses:
        '200':
          description: Success
          headers:
            Content-Type:
              description: The content type of response body
              type: string
          schema:
            type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /replication/adapters:
    get:
      summary: List registry adapters
      description: List registry adapters
      tags:
        - registry
      operationId: listRegistryProviderTypes
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Success.
          schema:
            type: array
            items:
              type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /replication/adapterinfos:
    get:
      summary: List all registered registry provider information
      description: List all registered registry provider information
      tags:
        - registry
      operationId: listRegistryProviderInfos
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Success.
          schema:
            type: object
            additionalProperties:
              $ref: '#/definitions/RegistryProviderInfo'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /registries:
    post:
      summary: Create a registry
      description: Create a registry
      tags:
        - registry
      operationId: createRegistry
      parameters:
        - $ref: '#/parameters/requestId'
        - name: registry
          in: body
          description: The registry
          required: true
          schema:
            $ref: '#/definitions/Registry'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
    get:
      summary: List the registries
      description: List the registries
      tags:
        - registry
      operationId: listRegistries
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: name
          in: query
          type: string
          required: false
          description: Deprecated, use `q` instead.
      responses:
        '200':
          description: Success
          headers:
            X-Total-Count:
              description: The total count of the resources
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/Registry'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /registries/ping:
    post:
      summary: Check status of a registry
      description: Check status of a registry
      tags:
        - registry
      operationId: pingRegistry
      parameters:
        - $ref: '#/parameters/requestId'
        - name: registry
          in: body
          description: The registry
          required: true
          schema:
            $ref: '#/definitions/RegistryPing'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /registries/{id}:
    get:
      summary: Get the specific registry
      description: Get the specific registry
      tags:
        - registry
      operationId: getRegistry
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Registry ID
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/Registry'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete the specific registry
      description: Delete the specific registry
      tags:
        - registry
      operationId: deleteRegistry
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Registry ID
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '412':
          $ref: '#/responses/412'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update the registry
      description: Update the registry
      tags:
        - registry
      operationId: updateRegistry
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: The registry ID
        - name: registry
          in: body
          description: The registry
          required: true
          schema:
            $ref: '#/definitions/RegistryUpdate'
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /registries/{id}/info:
    get:
      summary: Get the registry info
      description: Get the registry info
      tags:
        - registry
      operationId: getRegistryInfo
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Registry ID
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/RegistryInfo'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /scans/all/metrics:
    get:
      summary: Get the metrics of the latest scan all process
      description: Get the metrics of the latest scan all process
      tags:
        - scanAll
      operationId: getLatestScanAllMetrics
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Stats'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '412':
          $ref: '#/responses/412'
        '500':
          $ref: '#/responses/500'
  /scans/schedule/metrics:
    get:
      summary: Get the metrics of the latest scheduled scan all process
      description: Get the metrics of the latest scheduled scan all process
      tags:
        - scanAll
      operationId: getLatestScheduledScanAllMetrics
      deprecated: true
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Stats'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '412':
          $ref: '#/responses/412'
        '500':
          $ref: '#/responses/500'
  /systeminfo:
    get:
      summary: Get general system info
      operationId: getSystemInfo
      description: |
        This API is for retrieving general system info, this can be called by anonymous request.  Some attributes will be omitted in the response when this API is called by anonymous request.
      tags:
        - systeminfo
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get general info successfully.
          schema:
            $ref: '#/definitions/GeneralInfo'
        '500':
          $ref: '#/responses/500'
  /systeminfo/volumes:
    get:
      summary: Get system volume info (total/free size).
      operationId: getVolumes
      description: |
        This endpoint is for retrieving system volume info that only provides for admin user.  Note that the response only reflects the storage status of local disk.
      tags:
        - systeminfo
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get system volumes successfully.
          schema:
            $ref: '#/definitions/SystemInfo'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /systeminfo/getcert:
    get:
      summary: Get default root certificate.
      operationId: getCert
      description: |
        This endpoint is for downloading a default root certificate.
      tags:
        - systeminfo
      produces:
        - application/octet-stream
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get default root certificate successfully.
          schema:
            type: file
          headers:
            Content-Disposition:
              description: To set the filename of the downloaded file.
              type: string
        '404':
          description: Not found the default root certificate.
        '500':
          $ref: '#/responses/500'
  /system/oidc/ping:
    post:
      summary: Test the OIDC endpoint.
      description: |
        Test the OIDC endpoint, the setting of the endpoint is provided in the request.  This API can only be called by system admin.
      tags:
        - oidc
      operationId: pingOIDC
      parameters:
        - $ref: '#/parameters/requestId'
        - name: endpoint
          in: body
          description: Request body for OIDC endpoint to be tested.
          required: true
          schema:
            type: object
            properties:
              url:
                type: string
                description: The URL of OIDC endpoint to be tested.
              verify_cert:
                type: boolean
                description: Whether the certificate should be verified
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
  /system/gc:
    get:
      summary: Get gc results.
      description: This endpoint let user get gc execution history.
      tags:
        - gc
      operationId: getGCHistory
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: Get gc results successfully.
          headers:
            X-Total-Count:
              description: The total count of history
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/GCHistory'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /system/gc/{gc_id}:
    get:
      summary: Get gc status.
      description: This endpoint let user get gc status filtered by specific ID.
      operationId: getGC
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/gcId'
      tags:
        - gc
      responses:
        '200':
          description: Get gc results successfully.
          schema:
            $ref: '#/definitions/GCHistory'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Stop the specific GC execution
      description: Stop the GC execution specified by ID
      tags:
        - gc
      operationId: stopGC
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/gcId'
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /system/gc/{gc_id}/log:
    get:
      summary: Get gc job log.
      description: This endpoint let user get gc job logs filtered by specific ID.
      operationId: getGCLog
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/gcId'
      tags:
        - gc
      produces:
        - text/plain
      responses:
        '200':
          description: Get successfully.
          schema:
            type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /system/gc/schedule:
    get:
      summary: Get gc's schedule.
      description: This endpoint is for get schedule of gc job.
      operationId: getGCSchedule
      tags:
        - gc
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get gc's schedule.
          schema:
            $ref: '#/definitions/GCHistory'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create a gc schedule.
      description: |
        This endpoint is for update gc schedule.
      operationId: createGCSchedule
      parameters:
        - $ref: '#/parameters/requestId'
        - name: schedule
          in: body
          required: true
          schema:
            $ref: '#/definitions/Schedule'
          description: Updates of gc's schedule.
      tags:
        - gc
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update gc's schedule.
      description: |
        This endpoint is for update gc schedule.
      operationId: updateGCSchedule
      parameters:
        - $ref: '#/parameters/requestId'
        - name: schedule
          in: body
          required: true
          schema:
            $ref: '#/definitions/Schedule'
          description: Updates of gc's schedule.
      tags:
        - gc
      responses:
        '200':
          description: Updated gc's schedule successfully.
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /system/purgeaudit:
    get:
      summary: Get purge job results.
      description: get purge job execution history.
      tags:
        - purge
      operationId: getPurgeHistory
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: Get purge job results successfully.
          headers:
            X-Total-Count:
              description: The total count of history
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
          schema:
            type: array
            items:
              $ref: '#/definitions/ExecHistory'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /system/purgeaudit/{purge_id}:
    get:
      summary: Get purge job status.
      description: This endpoint let user get purge job status filtered by specific ID.
      operationId: getPurgeJob
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/purgeId'
      tags:
        - purge
      responses:
        '200':
          description: Get purge job results successfully.
          schema:
            $ref: '#/definitions/ExecHistory'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Stop the specific purge audit log execution
      description: Stop the purge audit log execution specified by ID
      tags:
        - purge
      operationId: stopPurge
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/purgeId'
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /system/purgeaudit/{purge_id}/log:
    get:
      summary: Get purge job log.
      description: This endpoint let user get purge job logs filtered by specific ID.
      operationId: getPurgeJobLog
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/purgeId'
      tags:
        - purge
      produces:
        - text/plain
      responses:
        '200':
          description: Get successfully.
          schema:
            type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /system/purgeaudit/schedule:
    get:
      summary: Get purge's schedule.
      description: This endpoint is for get schedule of purge job.
      operationId: getPurgeSchedule
      tags:
        - purge
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get purge job's schedule.
          schema:
            $ref: '#/definitions/ExecHistory'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create a purge job schedule.
      description: |
        This endpoint is for update purge job schedule.
      operationId: createPurgeSchedule
      parameters:
        - $ref: '#/parameters/requestId'
        - name: schedule
          in: body
          required: true
          schema:
            $ref: '#/definitions/Schedule'
          description: |
            The purge job's schedule, it is a json object. |
            The sample format is |
            {"parameters":{"audit_retention_hour":168,"dry_run":true, "include_operations":"create,delete,pull"},"schedule":{"type":"Hourly","cron":"0 0 * * * *"}} |
            the include_operation should be a comma separated string, e.g. create,delete,pull, if it is empty, no operation will be purged.
      tags:
        - purge
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update purge job's schedule.
      description: |
        This endpoint is for update purge job schedule.
      operationId: updatePurgeSchedule
      parameters:
        - $ref: '#/parameters/requestId'
        - name: schedule
          in: body
          required: true
          schema:
            $ref: '#/definitions/Schedule'
          description: |
            The purge job's schedule, it is a json object. |
            The sample format is |
            {"parameters":{"audit_retention_hour":168,"dry_run":true, "include_operations":"create,delete,pull"},"schedule":{"type":"Hourly","cron":"0 0 * * * *"}} |
            the include_operation should be a comma separated string, e.g. create,delete,pull, if it is empty, no operation will be purged.
      tags:
        - purge
      responses:
        '200':
          description: Updated purge's schedule successfully.
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'

  /system/CVEAllowlist:
    get:
      summary: Get the system level allowlist of CVE.
      description: Get the system level allowlist of CVE.  This API can be called by all authenticated users.
      operationId: getSystemCVEAllowlist
      tags:
        - SystemCVEAllowlist
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Successfully retrieved the CVE allowlist.
          schema:
            $ref: "#/definitions/CVEAllowlist"
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update the system level allowlist of CVE.
      description: This API overwrites the system level allowlist of CVE with the list in request body.  Only system Admin
        has permission to call this API.
      operationId: putSystemCVEAllowlist
      tags:
        - SystemCVEAllowlist
      parameters:
        - $ref: '#/parameters/requestId'
        - in: body
          name: allowlist
          description: The allowlist with new content
          schema:
            $ref: "#/definitions/CVEAllowlist"
      responses:
        '200':
          description: Successfully updated the CVE allowlist.
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /system/scanAll/schedule:
    get:
      summary: Get scan all's schedule.
      description: This endpoint is for getting a schedule for the scan all job, which scans all of images in Harbor.
      tags:
        - scanAll
      operationId: getScanAllSchedule
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get a schedule for the scan all job, which scans all of images in Harbor.
          schema:
            $ref: '#/definitions/Schedule'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '412':
          $ref: '#/responses/412'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update scan all's schedule.
      description: This endpoint is for updating the schedule of scan all job, which scans all of images in Harbor.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: schedule
          in: body
          required: true
          schema:
            $ref: '#/definitions/Schedule'
          description: Updates the schedule of scan all job, which scans all of images in Harbor.
      tags:
        - scanAll
      operationId: updateScanAllSchedule
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '412':
          $ref: '#/responses/412'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create a schedule or a manual trigger for the scan all job.
      description: This endpoint is for creating a schedule or a manual trigger for the scan all job, which scans all of images in Harbor.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: schedule
          in: body
          required: true
          schema:
            $ref: '#/definitions/Schedule'
          description: Create a schedule or a manual trigger for the scan all job.
      tags:
        - scanAll
      operationId: createScanAllSchedule
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '409':
          $ref: '#/responses/409'
        '412':
          $ref: '#/responses/412'
        '500':
          $ref: '#/responses/500'
  /system/scanAll/stop:
    post:
      summary: Stop scanAll job execution
      description: Stop scanAll job execution
      parameters:
        - $ref: '#/parameters/requestId'
      tags:
        - scanAll
      operationId: stopScanAll
      responses:
        '202':
          $ref: '#/responses/202'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /jobservice/pools:
    get:
      operationId: getWorkerPools
      summary: Get worker pools
      description: Get worker pools
      tags:
        - jobservice
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get worker pools successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/WorkerPool'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /jobservice/pools/{pool_id}/workers:
    get:
      operationId: getWorkers
      summary: Get workers
      description: Get workers in current pool
      tags:
        - jobservice
      parameters:
        - $ref: '#/parameters/requestId'
        - name: pool_id
          in: path
          required: true
          type: string
          description: The name of the pool. 'all' stands for all pools
      responses:
        '200':
          description: Get workers successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/Worker'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /jobservice/jobs/{job_id}:
    put:
      operationId: stopRunningJob
      summary: Stop running job
      description: Stop running job
      tags:
        - jobservice
      parameters:
        - $ref: '#/parameters/requestId'
        - name: job_id
          in: path
          required: true
          type: string
          description: The id of the job.
      responses:
        '200':
          description: Stop worker successfully.
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /jobservice/jobs/{job_id}/log:
    get:
      operationId: actionGetJobLog
      summary: Get job log by job id
      description: Get job log by job id, it is only used by administrator
      produces:
        - text/plain
      tags:
        - jobservice
      parameters:
        - $ref: '#/parameters/requestId'
        - name: job_id
          in: path
          required: true
          type: string
          description: The id of the job.
      responses:
        '200':
          description: Get job log successfully.
          headers:
            Content-Type:
              description: The content type of response body
              type: string
          schema:
            type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /jobservice/queues:
    get:
      operationId: listJobQueues
      summary: list job queues
      description: list job queue
      tags:
        - jobservice
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: List job queue successfully.
          schema:
            type: array
            items:
             $ref: '#/definitions/JobQueue'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /jobservice/queues/{job_type}:
    put:
      operationId: actionPendingJobs
      summary: stop and clean, pause, resume pending jobs in the queue
      description: stop and clean, pause, resume pending jobs in the queue
      tags:
        - jobservice
      parameters:
        - $ref: '#/parameters/requestId'
        - name: job_type
          in: path
          required: true
          type: string
          description: The type of the job. 'all' stands for all job types
        - name: action_request
          in: body
          required: true
          schema:
            $ref: '#/definitions/ActionRequest'
      responses:
        '200':
          description: take action to the jobs in the queue successfully.
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '422':
          $ref: '#/responses/422'
        '500':
          $ref: '#/responses/500'
  /schedules:
    get:
      operationId: listSchedules
      description: List schedules
      tags:
        - schedule
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: list schedule successfully.
          schema:
            type: array
            items:
              type: object
              $ref: '#/definitions/ScheduleTask'
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /schedules/{job_type}/paused:
    get:
      operationId: getSchedulePaused
      description: Get scheduler paused status
      tags:
        - schedule
      parameters:
        - $ref: '#/parameters/requestId'
        - name: job_type
          in: path
          required: true
          type: string
          description: The type of the job. 'all' stands for all job types, current only support query with all
      responses:
        '200':
          description: Get scheduler status successfully.
          schema:
            $ref: '#/definitions/SchedulerStatus'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /ping:
    get:
      operationId: getPing
      summary: Ping Harbor to check if the API server is alive.
      description: This API simply replies a pong to indicate the process to handle API is up, disregarding the health status of dependent components. This path does not require any authentication.
      tags:
        - ping
      produces:
        - text/plain
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: The API server is alive
          schema:
            type: string
  /retentions/metadatas:
    get:
      summary: Get Retention Metadatas
      description: Get Retention Metadatas.
      operationId: getRentenitionMetadata
      tags:
        - Retention
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get Retention Metadatas successfully.
          schema:
            $ref: '#/definitions/RetentionMetadata'

  /retentions:
    post:
      summary: Create Retention Policy
      operationId: createRetention
      description: >-
        Create Retention Policy, you can reference metadatas API for the policy model.
        You can check project metadatas to find whether a retention policy is already binded.
        This method should only be called when no retention policy binded to project yet.
      tags:
        - Retention
      parameters:
        - $ref: '#/parameters/requestId'
        - name: policy
          in: body
          description: Create Retention Policy successfully.
          required: true
          schema:
            $ref: '#/definitions/RetentionPolicy'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'

  /retentions/{id}:
    get:
      summary: Get Retention Policy
      operationId: getRetention
      description: Get Retention Policy.
      tags:
        - Retention
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Retention ID.
      responses:
        '200':
          description: Get Retention Policy successfully.
          schema:
            $ref: '#/definitions/RetentionPolicy'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update Retention Policy
      operationId: updateRetention
      description: >-
        Update Retention Policy, you can reference metadatas API for the policy model.
        You can check project metadatas to find whether a retention policy is already binded.
        This method should only be called when retention policy has already binded to project.
      tags:
        - Retention
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Retention ID.
        - name: policy
          in: body
          required: true
          schema:
            $ref: '#/definitions/RetentionPolicy'
      responses:
        '200':
          description: Update Retention Policy successfully.
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete Retention Policy
      operationId: deleteRetention
      description: >-
        Delete Retention Policy, you can reference metadatas API for the policy model.
        You can check project metadatas to find whether a retention policy is already binded.
        This method should only be called when retention policy has already binded to project.
      tags:
        - Retention
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Retention ID.
      responses:
        '200':
          description: Update Retention Policy successfully.
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'

  /retentions/{id}/executions:
    post:
      summary: Trigger a Retention Execution
      operationId: triggerRetentionExecution
      description: Trigger a Retention Execution, if dry_run is True, nothing would be deleted actually.
      tags:
        - Retention
      produces:
        - text/plain
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Retention ID.
        - name: body
          in: body
          required: true
          schema:
            type: object
            properties:
              dry_run:
                type: boolean
      responses:
        '200':
          description: Trigger a Retention job successfully.
        '201':
          $ref: '#/responses/201'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    get:
      summary: Get Retention executions
      operationId: listRetentionExecutions
      description: Get Retention executions, execution status may be delayed before job service schedule it up.
      tags:
        - Retention
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Retention ID.
        - name: page
          in: query
          type: integer
          format: int64
          required: false
          description: The page number.
        - name: page_size
          in: query
          type: integer
          format: int64
          required: false
          description: The size of per page.
      responses:
        '200':
          description: Get a Retention execution successfully.
          schema:
            type: array
            items:
              type: object
              $ref: '#/definitions/RetentionExecution'
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'

  /retentions/{id}/executions/{eid}:
    patch:
      summary: Stop a Retention execution
      operationId: operateRetentionExecution
      description: Stop a Retention execution, only support "stop" action now.
      tags:
        - Retention
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Retention ID.
        - name: eid
          in: path
          type: integer
          format: int64
          required: true
          description: Retention execution ID.
        - name: body
          in: body
          description: The action, only support "stop" now.
          required: true
          schema:
            type: object
            properties:
              action:
                type: string
      responses:
        '200':
          description: Stop a Retention job successfully.
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'

  /retentions/{id}/executions/{eid}/tasks:
    get:
      summary: Get Retention tasks
      operationId: listRetentionTasks
      description: Get Retention tasks, each repository as a task.
      tags:
        - Retention
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Retention ID.
        - name: eid
          in: path
          type: integer
          format: int64
          required: true
          description: Retention execution ID.
        - name: page
          in: query
          type: integer
          format: int64
          required: false
          description: The page number.
        - name: page_size
          in: query
          type: integer
          format: int64
          required: false
          description: The size of per page.
      responses:
        '200':
          description: Get Retention job tasks successfully.
          schema:
            type: array
            items:
              type: object
              $ref: '#/definitions/RetentionExecutionTask'
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'

  /retentions/{id}/executions/{eid}/tasks/{tid}:
    get:
      summary: Get Retention job task log
      operationId: getRetentionTaskLog
      description: Get Retention job task log, tags ratain or deletion detail will be shown in a table.
      tags:
        - Retention
      produces:
        - text/plain
      parameters:
        - $ref: '#/parameters/requestId'
        - name: id
          in: path
          type: integer
          format: int64
          required: true
          description: Retention ID.
        - name: eid
          in: path
          type: integer
          format: int64
          required: true
          description: Retention execution ID.
        - name: tid
          in: path
          type: integer
          format: int64
          required: true
          description: Retention execution ID.
      responses:
        '200':
          description: Get Retention job task log successfully.
          schema:
            type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'

  '/scanners':
    get:
      summary: List scanner registrations
      description: |
        Returns a list of currently configured scanner registrations.
      tags:
        - scanner
      operationId: listScanners
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: A list of scanner registrations.
          schema:
            type: array
            items:
              $ref: '#/definitions/ScannerRegistration'
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create a scanner registration
      description: |
        Creats a new scanner registration with the given data.
      tags:
        - scanner
      operationId: createScanner
      parameters:
        - $ref: '#/parameters/requestId'
        - name: registration
          in: body
          description: A scanner registration to be created.
          required: true
          schema:
            $ref: '#/definitions/ScannerRegistrationReq'
      responses:
        '201':
          description: Created successfully
          headers:
            Location:
              type: string
              description: The URL of the created resource
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/scanners/ping':
    post:
      summary: Tests scanner registration settings
      description: |
        Pings scanner adapter to test endpoint URL and authorization settings.
      tags:
        - scanner
      operationId: pingScanner
      parameters:
        - $ref: '#/parameters/requestId'
        - name: settings
          in: body
          description: A scanner registration settings to be tested.
          required: true
          schema:
            $ref: '#/definitions/ScannerRegistrationSettings'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/scanners/{registration_id}':
    get:
      summary: Get a scanner registration details
      description: |
        Retruns the details of the specified scanner registration.
      tags:
        - scanner
      operationId: getScanner
      parameters:
        - $ref: '#/parameters/requestId'
        - name: registration_id
          in: path
          description: The scanner registration identifer.
          required: true
          type: string
      responses:
        '200':
          description: The details of the scanner registration.
          schema:
            $ref: '#/definitions/ScannerRegistration'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update a scanner registration
      description: |
        Updates the specified scanner registration.
      tags:
        - scanner
      operationId: updateScanner
      parameters:
        - $ref: '#/parameters/requestId'
        - name: registration_id
          in: path
          description: The scanner registration identifier.
          required: true
          type: string
        - name: registration
          in: body
          required: true
          description: A scanner registraiton to be updated.
          schema:
            $ref: '#/definitions/ScannerRegistrationReq'
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete a scanner registration
      description: |
        Deletes the specified scanner registration.
      tags:
        - scanner
      operationId: deleteScanner
      parameters:
        - $ref: '#/parameters/requestId'
        - name: registration_id
          in: path
          description: The scanner registration identifier.
          required: true
          type: string
      responses:
        '200':
          description: Deleted successfully and return the deleted registration
          schema:
            $ref: '#/definitions/ScannerRegistration'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    patch:
      summary: Set system default scanner registration
      description: |
        Set the specified scanner registration as the system default one.
      tags:
        - scanner
      operationId: setScannerAsDefault
      parameters:
        - $ref: '#/parameters/requestId'
        - name: registration_id
          in: path
          description: The scanner registration identifier.
          required: true
          type: string
        - name: payload
          in: body
          required: true
          schema:
            $ref: '#/definitions/IsDefault'
      responses:
        '200':
          description: Successfully set the specified scanner registration as system default
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  '/scanners/{registration_id}/metadata':
    get:
      summary: Get the metadata of the specified scanner registration
      description: |
        Get the metadata of the specified scanner registration, including the capabilities and customized properties.
      tags:
        - scanner
      operationId: getScannerMetadata
      parameters:
        - $ref: '#/parameters/requestId'
        - name: registration_id
          in: path
          required: true
          description: The scanner registration identifier.
          type: string
      responses:
        '200':
          description: The metadata of the specified scanner adapter
          schema:
            $ref: '#/definitions/ScannerAdapterMetadata'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
  /users:
    get:
      summary: List users
      tags:
        - user
      operationId: listUsers
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
      responses:
        '200':
          description: return the list of users.
          schema:
            type: array
            items:
              $ref: '#/definitions/UserResp'
          headers:
            X-Total-Count:
              description: The total count of users
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Create a local user.
      description: This API can be used only when the authentication mode is for local DB.  When self registration is disabled.
      tags:
        - user
      operationId: createUser
      parameters:
        - $ref: '#/parameters/requestId'
        - name: userReq
          in: body
          description: The new user
          required: true
          schema:
            $ref: '#/definitions/UserCreationReq'
      responses:
        '201':
          $ref: '#/responses/201'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          description: When the  self registration is disabled, non-admin does not have permission to create user.  When self registration is enabled, this API can only be called from UI portal, calling it via script will get a 403 error.
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /users/current:
    get:
      summary: Get current user info.
      tags:
        - user
      operationId: getCurrentUserInfo
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get current user information successfully.
          schema:
            $ref: '#/definitions/UserResp'
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
  /users/search:
    get:
      summary: Search users by username
      description: |
        This endpoint is to search the users by username.  It's open for all authenticated requests.
      tags:
        - user
      operationId: searchUsers
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: username
          in: query
          type: string
          required: true
          description: Username for filtering results.
      responses:
        '200':
          description: Search users by username successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/UserSearchRespItem'
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
  '/users/{user_id}':
    get:
      summary: Get a user's profile.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: user_id
          in: path
          type: integer
          format: int
          required: true
      tags:
        - user
      operationId: getUser
      responses:
        '200':
          description: Get user's info successfully.
          schema:
            $ref: '#/definitions/UserResp'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update user's profile.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: user_id
          in: path
          type: integer
          format: int
          required: true
          description: Registered user ID
        - name: profile
          in: body
          description: 'Only email, realname and comment can be modified.'
          required: true
          schema:
            $ref: '#/definitions/UserProfile'
      tags:
        - user
      operationId: updateUserProfile
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Mark a registered user as be removed.
      description: |
        This endpoint let administrator of Harbor mark a registered user as removed.It actually won't be deleted from DB.
      parameters:
        - $ref: '#/parameters/requestId'
        - name: user_id
          in: path
          type: integer
          format: int
          required: true
          description: User ID for marking as to be removed.
      tags:
        - user
      operationId: deleteUser
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /users/{user_id}/sysadmin:
    put:
      summary: Update a registered user to change to be an administrator of Harbor.
      tags:
       - user
      operationId: setUserSysAdmin
      parameters:
        - $ref: '#/parameters/requestId'
        - name: user_id
          in: path
          type: integer
          format: int
          required: true
        - name: sysadmin_flag
          in: body
          description: Toggle a user to admin or not.
          required: true
          schema:
            $ref: '#/definitions/UserSysAdminFlag'
      responses:
        '200':
          $ref: '#/responses/200'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          description: Unexpected internal errors.
  '/users/{user_id}/password':
    put:
      summary: Change the password on a user that already exists.
      description: |
        This endpoint is for user to update password. Users with the admin role can change any user's password. Regular users can change only their own password.
      tags:
        - user
      operationId: updateUserPassword
      parameters:
        - $ref: '#/parameters/requestId'
        - name: user_id
          in: path
          type: integer
          format: int
          required: true
        - name: password
          in: body
          description: Password to be updated, the attribute 'old_password' is optional when the API is called by the system administrator.
          required: true
          schema:
            $ref: '#/definitions/PasswordReq'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          description: Invalid user ID; Password does not meet requirement
        '401':
          $ref: '#/responses/401'
        '403':
          description: The caller does not have permission to update the password of the user with given ID, or the old password in request body is not correct.
        '500':
          $ref: '#/responses/500'
  /users/current/permissions:
    get:
      summary: Get current user permissions.
      tags:
        - user
      operationId: getCurrentUserPermissions
      parameters:
        - $ref: '#/parameters/requestId'
        - name: scope
          in: query
          type: string
          required: false
          description: The scope for the permission
        - name: relative
          in: query
          type: boolean
          required: false
          description: |
            If true, the resources in the response are relative to the scope,
            eg for resource '/project/1/repository' if relative is 'true' then the resource in response will be 'repository'.
      responses:
        '200':
          description: Get current user permission successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/Permission'
        '401':
          description: User need to log in first.
        '500':
          description: Internal errors.
  '/users/{user_id}/cli_secret':
    put:
      summary: Set CLI secret for a user.
      description: >-
        This endpoint let user generate a new CLI secret for himself.  This API only works when auth mode is set to 'OIDC'.
        Once this API returns with successful status, the old secret will be invalid, as there will be only one CLI secret
        for a user.
      tags:
        - user
      operationId: setCliSecret
      parameters:
        - $ref: '#/parameters/requestId'
        - name: user_id
          in: path
          type: integer
          format: int
          required: true
          description: User ID
        - name: secret
          in: body
          required: true
          schema:
            $ref: '#/definitions/OIDCCliSecretReq'
      responses:
        '200':
          description: The secret is successfully updated
        '400':
          description: Invalid user ID.  Or user is not onboarded via OIDC authentication. Or the secret does not meet the standard.
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '412':
          description: The auth mode of the system is not "oidc_auth", or the user is not onboarded via OIDC AuthN.
        '500':
          $ref: '#/responses/500'

  /labels:
    get:
      summary: List labels according to the query strings.
      description: |
        This endpoint let user list labels by name, scope and project_id
      tags:
        - label
      operationId: ListLabels
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/sort'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: name
          in: query
          type: string
          required: false
          description: The label name.
        - name: scope
          in: query
          type: string
          required: false
          description: The label scope. Valid values are g and p. g for global labels and p for project labels.
        - name: project_id
          in: query
          type: integer
          format: int64
          required: false
          description: Relevant project ID, required when scope is p.
      responses:
        '200':
          description: Get successfully.
          schema:
            type: array
            items:
              $ref: '#/definitions/Label'
          headers:
            X-Total-Count:
              description: The total count of available items
              type: integer
            Link:
              description: Link to previous page and next page
              type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'
    post:
      summary: Post creates a label
      description: |
        This endpoint let user creates a label.
      tags:
        - label
      operationId: CreateLabel
      parameters:
        - $ref: '#/parameters/requestId'
        - name: label
          in: body
          description: The json object of label.
          required: true
          schema:
            $ref: '#/definitions/Label'
      responses:
        '201':
          description: Create successfully.
          headers:
            Location:
              type: string
              description: The URL of the created resource
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '409':
          $ref: '#/responses/409'
        '415':
          $ref: '#/responses/415'
        '500':
          $ref: '#/responses/500'
  '/labels/{label_id}':
    get:
      summary: Get the label specified by ID.
      description: |
        This endpoint let user get the label by specific ID.
      tags:
        - label
      operationId: GetLabelByID
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/labelId'
      responses:
        '200':
          description: Get successfully.
          schema:
            $ref: '#/definitions/Label'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
    put:
      summary: Update the label properties.
      description: |
        This endpoint let user update label properties.
      tags:
        - label
      operationId: UpdateLabel
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/labelId'
        - name: label
          in: body
          description: The updated label json object.
          required: true
          schema:
            $ref: '#/definitions/Label'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
    delete:
      summary: Delete the label specified by ID.
      description: |
        Delete the label specified by ID.
      tags:
        - label
      operationId: DeleteLabel
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/labelId'
      responses:
        '200':
          $ref: '#/responses/200'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'

  /export/cve:
    post:
      summary: Export scan data for selected projects
      description: Export scan data for selected projects
      tags:
        - scan data export
      operationId: exportScanData
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/scanDataType'
        - name: criteria
          in: body
          description: The criteria for the export
          required: true
          schema:
            $ref: '#/definitions/ScanDataExportRequest'
      responses:
        '200':
          description: Success.
          schema:
            $ref: '#/definitions/ScanDataExportJob'
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '405':
          $ref: '#/responses/405'
        '409':
          $ref: '#/responses/409'
        '500':
          $ref: '#/responses/500'
  /export/cve/execution/{execution_id}:
    get:
      summary: Get the specific scan data export execution
      description: Get the scan data export execution specified by ID
      tags:
        - scan data export
      operationId: getScanDataExportExecution
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/executionId'
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/ScanDataExportExecution'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /export/cve/executions:
    get:
      summary: Get a list of specific scan data export execution jobs for a specified user
      description: Get a list of specific scan data export execution jobs for a specified user
      tags:
        - scan data export
      operationId: getScanDataExportExecutionList
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/ScanDataExportExecutionList'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /export/cve/download/{execution_id}:
    get:
      summary: Download the scan data export file
      description: Download the scan data report. Default format is CSV
      tags:
        - scan data export
      operationId: downloadScanData
      produces:
        - text/csv
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/executionId'
        - name: format
          in: query
          type: string
          required: false
          description: The format of the data to be exported. e.g. CSV or PDF
      responses:
        '200':
          description: Data file containing the export data
          schema:
            type: file
          headers:
            Content-Disposition:
              type: string
              description: Value is a CSV formatted file; filename=export.csv
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'
  /security/summary:
    get:
      summary: Get vulnerability system summary
      description: Retrieve the vulnerability summary of the system
      tags:
        - securityhub
      operationId: getSecuritySummary
      parameters:
        - $ref: '#/parameters/requestId'
        - name: with_dangerous_cve
          in: query
          description: Specify whether the dangerous CVEs are included inside summary information
          type: boolean
          required: false
          default: false
        - name: with_dangerous_artifact
          in: query
          description: Specify whether the dangerous Artifact are included inside summary information
          type: boolean
          required: false
          default: false
      responses:
        '200':
          description: Success
          schema:
            $ref: '#/definitions/SecuritySummary'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'

  /security/vul:
    get:
      summary: Get the vulnerability list.
      description: |
        Get the vulnerability list. use q to pass the query condition,
        supported conditions:
        cve_id(exact match)
        cvss_score_v3(range condition)
        severity(exact match)
        repository_name(exact match)
        project_id(exact match)
        package(exact match)
        tag(exact match)
        digest(exact match)
      tags:
        - securityhub
      operationId: ListVulnerabilities
      parameters:
        - $ref: '#/parameters/requestId'
        - $ref: '#/parameters/query'
        - $ref: '#/parameters/page'
        - $ref: '#/parameters/pageSize'
        - name: tune_count
          in: query
          description: Enable to ignore X-Total-Count when the total count > 1000, if the total count is less than 1000, the real total count is returned, else -1.
          type: boolean
          required: false
          default: false
        - name: with_tag
          in: query
          description: Specify whether the tag information is included inside vulnerability information
          type: boolean
          required: false
          default: false
      responses:
        '200':
          description: The vulnerability list.
          schema:
            type: array
            items:
              $ref: '#/definitions/VulnerabilityItem'
          headers:
            X-Total-Count:
              description: The total count of vulnerabilities
              type: integer
            Link:
              description: Link refers to the previous page and next page
              type: string
        '400':
          $ref: '#/responses/400'
        '401':
          $ref: '#/responses/401'
        '500':
          $ref: '#/responses/500'

  /permissions:
    get:
      summary: Get system or project level permissions info.
      operationId: getPermissions
      description: |
        This endpoint is for retrieving resource and action info that only provides for admin user(system admin and project admin).
      tags:
        - permissions
      parameters:
        - $ref: '#/parameters/requestId'
      responses:
        '200':
          description: Get permissions successfully.
          schema:
            $ref: '#/definitions/Permissions'
        '401':
          $ref: '#/responses/401'
        '403':
          $ref: '#/responses/403'
        '404':
          $ref: '#/responses/404'
        '500':
          $ref: '#/responses/500'

parameters:
  query:
    name: q
    description: Query string to query resources. Supported query patterns are "exact match(k=v)", "fuzzy match(k=~v)", "range(k=[min~max])", "list with union releationship(k={v1 v2 v3})" and "list with intersetion relationship(k=(v1 v2 v3))". The value of range and list can be string(enclosed by " or '), integer or time(in format "2020-04-09 02:36:00"). All of these query patterns should be put in the query string "q=xxx" and splitted by ",". e.g. q=k1=v1,k2=~v2,k3=[min~max]
    in: query
    type: string
    required: false
  sort:
    name: sort
    description: Sort the resource list in ascending or descending order. e.g. sort by field1 in ascending order and field2 in descending order with "sort=field1,-field2"
    in: query
    type: string
    required: false
  page:
    name: page
    in: query
    type: integer
    format: int64
    required: false
    description: The page number
    default: 1
  pageSize:
    name: page_size
    in: query
    type: integer
    format: int64
    required: false
    description: The size of per page
    default: 10
    maximum: 100
  requestId:
    name: X-Request-Id
    description: An unique ID for the request
    in: header
    type: string
    required: false
    minLength: 1
  resourceNameInLocation:
    name: X-Resource-Name-In-Location
    description: The flag to indicate whether to return the name of the resource in Location. When X-Resource-Name-In-Location is true, the Location will return the name of the resource.
    in: header
    type: boolean
    required: false
    default: false
  isResourceName:
    name: X-Is-Resource-Name
    description: The flag to indicate whether the parameter which supports both name and id in the path is the name of the resource. When the X-Is-Resource-Name is false and the parameter can be converted to an integer, the parameter will be as an id, otherwise, it will be as a name.
    in: header
    type: boolean
    required: false
    default: false
  acceptVulnerabilities:
    name: X-Accept-Vulnerabilities
    in: header
    type: string
    default: 'application/vnd.security.vulnerability.report; version=1.1, application/vnd.scanner.adapter.vuln.report.harbor+json; version=1.0'
    description: |-
      A comma-separated lists of MIME types for the scan report or scan summary. The first mime type will be used when the report found for it.
      Currently the mime type supports 'application/vnd.scanner.adapter.vuln.report.harbor+json; version=1.0' and 'application/vnd.security.vulnerability.report; version=1.1'
  projectName:
    name: project_name
    in: path
    description: The name of the project
    required: true
    type: string
  projectNameOrId:
    name: project_name_or_id
    in: path
    description: The name or id of the project
    required: true
    type: string
  repositoryName:
    name: repository_name
    in: path
    description: The name of the repository. If it contains slash, encode it twice over with URL encoding. e.g. a/b -> a%2Fb -> a%252Fb
    required: true
    type: string
  reference:
    name: reference
    in: path
    description: The reference of the artifact, can be digest or tag
    required: true
    type: string
  digest:
    name: digest
    in: path
    description: The digest of the resource
    required: true
    type: string
  tagName:
    name: tag_name
    in: path
    description: The name of the tag
    required: true
    type: string
  instanceName:
    name: preheat_instance_name
    in: path
    description: Instance Name
    required: true
    type: string
  preheatPolicyName:
    name: preheat_policy_name
    in: path
    description: Preheat Policy Name
    required: true
    type: string
  executionId:
    name: execution_id
    in: path
    description: Execution ID
    required: true
    type: integer
  taskId:
    name: task_id
    in: path
    description: Task ID
    required: true
    type: integer
  robotId:
    name: robot_id
    in: path
    description: Robot ID
    required: true
    type: integer
  gcId:
    name: gc_id
    in: path
    description: The ID of the gc log
    required: true
    type: integer
    format: int64
  purgeId:
    name: purge_id
    in: path
    description: The ID of the purge log
    required: true
    type: integer
    format: int64
  labelId:
    name: label_id
    in: path
    description: Label ID
    required: true
    type: integer
    format: int64
  webhookPolicyId:
    name: webhook_policy_id
    in: path
    description: The ID of the webhook policy
    required: true
    type: integer
    format: int64
  immutableRuleId:
    name: immutable_rule_id
    in: path
    description: The ID of the immutable rule
    required: true
    type: integer
    format: int64
  accessoryId:
    name: accessory_id
    in: path
    description: The ID of the accessory
    required: true
    type: integer
    format: int64
  scanDataType:
    name: X-Scan-Data-Type
    description: The type of scan data to export
    in: header
    type: string
    required: true
  userName:
    name: user_name
    description: The name of the user
    in: query
    type: string
    required: true

responses:
  '200':
    description: Success
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
  '201':
    description: Created
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
      Location:
        description: The location of the resource
        type: string
  '202':
    description: Accepted
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
  '400':
    description: Bad request
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'
  '401':
    description: Unauthorized
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'
  '403':
    description: Forbidden
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'
  '404':
    description: Not found
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'
  '405':
    description: Method not allowed
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'
  '409':
    description: Conflict
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'
  '412':
    description: Precondition failed
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'
  '415':
    description: Unsupported MediaType
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'
  '422':
    description: Unsupported Type
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'    
  '500':
    description: Internal server error
    headers:
      X-Request-Id:
        description: The ID of the corresponding request for the response
        type: string
    schema:
      $ref: '#/definitions/Errors'
definitions:
  Errors:
    description: The error array that describe the errors got during the handling of request
    type: object
    properties:
      errors:
        type: array
        items:
          $ref: '#/definitions/Error'
  Error:
    description: a model for all the error response coming from harbor
    type: object
    properties:
      code:
        type: string
        description: The error code
      message:
        type: string
        description: The error message
  Search:
    type: object
    properties:
      project:
        description: Search results of the projects that matched the filter keywords.
        type: array
        items:
          $ref: '#/definitions/Project'
      repository:
        description: Search results of the repositories that matched the filter keywords.
        type: array
        items:
          $ref: '#/definitions/SearchRepository'
  SearchRepository:
    type: object
    properties:
      project_id:
        type: integer
        description: The ID of the project that the repository belongs to
      project_name:
        type: string
        description: The name of the project that the repository belongs to
      project_public:
        type: boolean
        description: 'The flag to indicate the publicity of the project that the repository belongs to (1 is public, 0 is not)'
      repository_name:
        type: string
        description: The name of the repository
      pull_count:
        type: integer
        description: The count how many times the repository is pulled
      artifact_count:
        type: integer
        description: The count of artifacts in the repository
  Repository:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: The ID of the repository
      project_id:
        type: integer
        format: int64
        description: The ID of the project that the repository belongs to
      name:
        type: string
        description: The name of the repository
      description:
        type: string
        description: The description of the repository
      artifact_count:
        type: integer
        format: int64
        description: The count of the artifacts inside the repository
        x-omitempty: false
      pull_count:
        type: integer
        format: int64
        description: The count that the artifact inside the repository pulled
        x-omitempty: false
      creation_time:
        type: string
        format: date-time
        description: The creation time of the repository
        x-nullable: true
        x-omitempty: true
      update_time:
        type: string
        format: date-time
        description: The update time of the repository
  Artifact:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: The ID of the artifact
      type:
        type: string
        description: The type of the artifact, e.g. image, chart, etc
      media_type:
        type: string
        description: The media type of the artifact
      manifest_media_type:
        type: string
        description: The manifest media type of the artifact
      artifact_type:
        type: string
        description: The artifact_type in the manifest of the artifact
      project_id:
        type: integer
        format: int64
        description: The ID of the project that the artifact belongs to
      repository_id:
        type: integer
        format: int64
        description: The ID of the repository that the artifact belongs to
      repository_name:
        type: string
        description: The name of the repository that the artifact belongs to
      digest:
        type: string
        description: The digest of the artifact
      size:
        type: integer
        format: int64
        description: The size of the artifact
      icon:
        type: string
        description: The digest of the icon
      push_time:
        type: string
        format: date-time
        description: The push time of the artifact
      pull_time:
        type: string
        format: date-time
        description: The latest pull time of the artifact
      extra_attrs:
        $ref: '#/definitions/ExtraAttrs'
      annotations:
        $ref: '#/definitions/Annotations'
      references:
        type: array
        items:
          $ref: '#/definitions/Reference'
      tags:
        type: array
        items:
          $ref: '#/definitions/Tag'
      addition_links:
        $ref: '#/definitions/AdditionLinks'
      labels:
        type: array
        items:
          $ref: '#/definitions/Label'
      scan_overview:
        $ref: '#/definitions/ScanOverview'
        description: The overview of the scan result.
      sbom_overview:
        $ref: '#/definitions/SBOMOverview'
        description: The overview of the generating SBOM progress
      accessories:
        type: array
        items:
          $ref: '#/definitions/Accessory'
          description: The accessory of the artifact.
  Tag:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: The ID of the tag
      repository_id:
        type: integer
        format: int64
        description: The ID of the repository that the tag belongs to
      artifact_id:
        type: integer
        format: int64
        description: The ID of the artifact that the tag attached to
      name:
        type: string
        description: The name of the tag
      push_time:
        type: string
        format: date-time
        description: The push time of the tag
      pull_time:
        type: string
        format: date-time
        description: The latest pull time of the tag
      immutable:
        type: boolean
        x-omitempty: false
        description: The immutable status of the tag
  ExtraAttrs:
    type: object
    additionalProperties:
      type: object
  Annotations:
    type: object
    additionalProperties:
      type: string
  AdditionLinks:
    type: object
    additionalProperties:
      $ref: '#/definitions/AdditionLink'
  AdditionLink:
    type: object
    properties:
      href:
        type: string
        description: The link of the addition
      absolute:
        type: boolean
        x-omitempty: false
        description: Determine whether the link is an absolute URL or not
  Reference:
    type: object
    properties:
      parent_id:
        type: integer
        format: int64
        description: The parent ID of the reference
      child_id:
        type: integer
        format: int64
        description: The child ID of the reference
      child_digest:
        type: string
        description: The digest of the child artifact
      platform:
        $ref: '#/definitions/Platform'
      annotations:
        $ref: '#/definitions/Annotations'
      urls:
        type: array
        description: The download URLs
        items:
          type: string
  Platform:
    type: object
    properties:
      architecture:
        type: string
        description: The architecture that the artifact applys to
      os:
        type: string
        description: The OS that the artifact applys to
      '''os.version''':
        type: string
        description: The version of the OS that the artifact applys to
      '''os.features''':
        type: array
        description: The features of the OS that the artifact applys to
        items:
          type: string
      variant:
        type: string
        description: The variant of the CPU
  Label:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: The ID of the label
      name:
        type: string
        description: The name the label
      description:
        type: string
        description: The description the label
      color:
        type: string
        description: The color the label
      scope:
        type: string
        description: The scope the label
      project_id:
        type: integer
        format: int64
        description: The ID of project that the label belongs to
      creation_time:
        type: string
        format: date-time
        description: The creation time the label
      update_time:
        type: string
        format: date-time
        description: The update time of the label
  Scanner:
    type: object
    properties:
      name:
        type: string
        description: Name of the scanner
        example: "Trivy"
      vendor:
        type: string
        description: Name of the scanner provider
        example: "Aqua Security"
      version:
        type: string
        description: Version of the scanner adapter
        example: "v0.9.1"
  ScanOverview:
    type: object
    description: 'The scan overview attached in the metadata of tag'
    additionalProperties:
      $ref: '#/definitions/NativeReportSummary'
  SBOMOverview:
    type: object
    description: 'The generate SBOM overview information'
    properties:
      start_time:
        type: string
        format: date-time
        description: 'The start time of the generating sbom report task'
        example: '2006-01-02T14:04:05Z'
      end_time:
        type: string
        format: date-time
        description: 'The end time of the generating sbom report task'
        example: '2006-01-02T15:04:05Z'
      scan_status:
        type: string
        description: 'The status of the generating SBOM task'
      sbom_digest:
        type: string
        description: 'The digest of the generated SBOM accessory'
      report_id:
        type: string
        description: 'id of the native scan report'
        example: '5f62c830-f996-11e9-957f-0242c0a89008'
      duration:
        type: integer
        format: int64
        description: 'Time in seconds required to create the report'
        example: 300
      scanner:
        $ref: '#/definitions/Scanner'
  NativeReportSummary:
    type: object
    description: 'The summary for the native report'
    properties:
      report_id:
        type: string
        description: 'id of the native scan report'
        example: '5f62c830-f996-11e9-957f-0242c0a89008'
      scan_status:
        type: string
        description: 'The status of the report generating process'
        example: 'Success'
      severity:
        type: string
        description: 'The overall severity'
        example: 'High'
      duration:
        type: integer
        format: int64
        description: 'The seconds spent for generating the report'
        example: 300
      summary:
        $ref: '#/definitions/VulnerabilitySummary'
      start_time:
        type: string
        format: date-time
        description: 'The start time of the scan process that generating report'
        example: '2006-01-02T14:04:05Z'
      end_time:
        type: string
        format: date-time
        description: 'The end time of the scan process that generating report'
        example: '2006-01-02T15:04:05Z'
      complete_percent:
        type: integer
        description: 'The complete percent of the scanning which value is between 0 and 100'
        example: 100
      scanner:
        $ref: '#/definitions/Scanner'
  VulnerabilitySummary:
    type: object
    description: |
      VulnerabilitySummary contains the total number of the foun d vulnerabilities number and numbers of each severity level.
    properties:
      total:
        type: integer
        format: int
        description: 'The total number of the found vulnerabilities'
        example: 500
        x-omitempty: false
      fixable:
        type: integer
        format: int
        description: 'The number of the fixable vulnerabilities'
        example: 100
        x-omitempty: false
      summary:
        type: object
        description: 'Numbers of the vulnerabilities with different severity'
        additionalProperties:
          type: integer
          format: int
          example: 10
        example:
          'Critical': 5
          'High': 5
        x-omitempty: false
  AuditLog:
    type: object
    properties:
      id:
        type: integer
        description: The ID of the audit log entry.
      username:
        type: string
        description: Username of the user in this log entry.
      resource:
        type: string
        description: Name of the repository in this log entry.
      resource_type:
        type: string
        description: Tag of the repository in this log entry.
      operation:
        type: string
        description: The operation against the repository in this log entry.
      op_time:
        type: string
        format: date-time
        example: '2006-01-02T15:04:05Z'
        description: The time when this operation is triggered.
  Metadata:
    type: object
    properties:
      id:
        type: string
        description: id
      name:
        type: string
        description: name
      icon:
        type: string
        description: icon
      maintainers:
        type: array
        description: maintainers
        items:
          type: string
      version:
        type: string
        description: version
      source:
        type: string
        description: source
  Instance:
    type: object
    properties:
      id:
        type: integer
        description: Unique ID
      name:
        type: string
        description: Instance name
      description:
        type: string
        description: Description of instance
      vendor:
        type: string
        description: Based on which driver, identified by ID
      endpoint:
        type: string
        description: The service endpoint of this instance
      auth_mode:
        type: string
        description: The authentication way supported
      auth_info:
        type: object
        description: The auth credential data if exists
        additionalProperties:
          type: string
      status:
        type: string
        description: The health status
      enabled:
        type: boolean
        description: Whether the instance is activated or not
        x-omitempty: false
      default:
        type: boolean
        description: Whether the instance is default or not
        x-omitempty: false
      insecure:
        type: boolean
        description: Whether the instance endpoint is insecure or not
        x-omitempty: false
      setup_timestamp:
        type: integer
        format: int64
        description: The timestamp of instance setting up
  PreheatPolicy:
    type: object
    properties:
      id:
        type: integer
        description: The ID of preheat policy
      name:
        type: string
        description: The Name of preheat policy
      description:
        type: string
        description: The Description of preheat policy
      project_id:
        type: integer
        description: The ID of preheat policy project
      provider_id:
        type: integer
        description: The ID of preheat policy provider
      provider_name:
        type: string
        description: The Name of preheat policy provider
      filters:
        type: string
        description: The Filters of preheat policy
      trigger:
        type: string
        description: The Trigger of preheat policy
      enabled:
        type: boolean
        description: Whether the preheat policy enabled
        x-omitempty: false
      scope:
        type: string
        description: The scope of preheat policy
      creation_time:
        type: string
        format: date-time
        description: The Create Time of preheat policy
      update_time:
        type: string
        format: date-time
        description: The Update Time of preheat policy
  Metrics:
    type: object
    properties:
      task_count:
        type: integer
        description: The count of task
      success_task_count:
        type: integer
        description: The count of success task
      error_task_count:
        type: integer
        description: The count of error task
      pending_task_count:
        type: integer
        description: The count of pending task
      running_task_count:
        type: integer
        description: The count of running task
      scheduled_task_count:
        type: integer
        description: The count of scheduled task
      stopped_task_count:
        type: integer
        description: The count of stopped task
  Execution:
    type: object
    properties:
      id:
        type: integer
        description: The ID of execution
      vendor_type:
        type: string
        description: The vendor type of execution
      vendor_id:
        type: integer
        description: The vendor id of execution
      status:
        type: string
        description: The status of execution
      status_message:
        type: string
        description: The status message of execution
      metrics:
        $ref: '#/definitions/Metrics'
      trigger:
        type: string
        description: The trigger of execution
      extra_attrs:
        $ref: '#/definitions/ExtraAttrs'
      start_time:
        type: string
        description: The start time of execution
      end_time:
        type: string
        description: The end time of execution
  Task:
    type: object
    properties:
      id:
        type: integer
        description: The ID of task
      execution_id:
        type: integer
        description: The ID of task execution
      status:
        type: string
        description: The status of task
      status_message:
        type: string
        description: The status message of task
      run_count:
        type: integer
        format: int32
        description: The count of task run
      extra_attrs:
        $ref: '#/definitions/ExtraAttrs'
      creation_time:
        type: string
        description: The creation time of task
      update_time:
        type: string
        description: The update time of task
      start_time:
        type: string
        description: The start time of task
      end_time:
        type: string
        description: The end time of task
  ProviderUnderProject:
    type: object
    properties:
      id:
        type: integer
      provider:
        type: string
      enabled:
        type: boolean
      default:
        type: boolean
  Icon:
    type: object
    properties:
      content-type:
        type: string
        description: The content type of the icon
      content:
        type: string
        description: The base64 encoded content of the icon
  ProjectReq:
    type: object
    properties:
      project_name:
        type: string
        description: The name of the project.
        maxLength: 255
      public:
        type: boolean
        description: deprecated, reserved for project creation in replication
        x-nullable: true
      metadata:
        description: The metadata of the project.
        $ref: '#/definitions/ProjectMetadata'
      cve_allowlist:
        description: The CVE allowlist of the project.
        $ref: '#/definitions/CVEAllowlist'
      storage_limit:
        type: integer
        format: int64
        description: The storage quota of the project.
        x-nullable: true
      registry_id:
        type: integer
        format: int64
        description: The ID of referenced registry when creating the proxy cache project
        x-nullable: true
  Project:
    type: object
    properties:
      project_id:
        type: integer
        format: int32
        description: Project ID
      owner_id:
        type: integer
        format: int32
        description: The owner ID of the project always means the creator of the project.
      name:
        type: string
        description: The name of the project.
      registry_id:
        type: integer
        format: int64
        description: The ID of referenced registry when the project is a proxy cache project.
      creation_time:
        type: string
        format: date-time
        description: The creation time of the project.
      update_time:
        type: string
        format: date-time
        description: The update time of the project.
      deleted:
        type: boolean
        description: A deletion mark of the project.
      owner_name:
        type: string
        description: The owner name of the project.
      togglable:
        type: boolean
        description: Correspond to the UI about whether the project's publicity is  updatable (for UI)
      current_user_role_id:
        type: integer
        description: The role ID with highest permission of the current user who triggered the API (for UI).  This attribute is deprecated and will be removed in future versions.
      current_user_role_ids:
        type: array
        items:
          type: integer
          format: int32
        description: The list of role ID of the current user who triggered the API (for UI)
      repo_count:
        type: integer
        description: The number of the repositories under this project.
        x-omitempty: false
      metadata:
        description: The metadata of the project.
        $ref: '#/definitions/ProjectMetadata'
      cve_allowlist:
        description: The CVE allowlist of this project.
        $ref: '#/definitions/CVEAllowlist'
  ProjectDeletable:
    type: object
    properties:
      deletable:
        type: boolean
        description: Whether the project can be deleted.
      message:
        type: string
        description: The detail message when the project can not be deleted.
  ProjectMetadata:
    type: object
    properties:
      public:
        type: string
        description: 'The public status of the project. The valid values are "true", "false".'
      enable_content_trust:
        type: string
        description: 'Whether content trust is enabled or not. If it is enabled, user can''t pull unsigned images from this project. The valid values are "true", "false".'
        x-nullable: true
      enable_content_trust_cosign:
        type: string
        description: 'Whether cosign content trust is enabled or not. If it is enabled, user can''t pull images without cosign signature from this project. The valid values are "true", "false".'
        x-nullable: true
      prevent_vul:
        type: string
        description: 'Whether prevent the vulnerable images from running. The valid values are "true", "false".'
        x-nullable: true
      severity:
        type: string
        description: 'If the vulnerability is high than severity defined here, the images can''t be pulled. The valid values are "none", "low", "medium", "high", "critical".'
        x-nullable: true
      auto_scan:
        type: string
        description: 'Whether scan images automatically when pushing. The valid values are "true", "false".'
        x-nullable: true
      auto_sbom_generation:
        type: string
        description: 'Whether generating SBOM automatically when pushing a subject artifact. The valid values are "true", "false".'
        x-nullable: true
      reuse_sys_cve_allowlist:
        type: string
        description: 'Whether this project reuse the system level CVE allowlist as the allowlist of its own.  The valid values are "true", "false".
        If it is set to "true" the actual allowlist associate with this project, if any, will be ignored.'
        x-nullable: true
      retention_id:
        type: string
        description: 'The ID of the tag retention policy for the project'
        x-nullable: true
      proxy_speed_kb:
        type: string
        description: 'The bandwidth limit of proxy cache, in Kbps (kilobits per second). It limits the communication between Harbor and the upstream registry, not the client and the Harbor.'
        x-nullable: true
  ProjectSummary:
    type: object
    properties:
      repo_count:
        type: integer
        description: The number of the repositories under this project.
        x-omitempty: false
      project_admin_count:
        type: integer
        description: The total number of project admin members.
      maintainer_count:
        type: integer
        description: The total number of maintainer members.
      developer_count:
        type: integer
        description: The total number of developer members.
      guest_count:
        type: integer
        description: The total number of guest members.
      limited_guest_count:
        type: integer
        description: The total number of limited guest members.
      quota:
        $ref: "#/definitions/ProjectSummaryQuota"
      registry:
        $ref: "#/definitions/Registry"
  ProjectSummaryQuota:
    type: object
    properties:
      hard:
        $ref: "#/definitions/ResourceList"
        description: The hard limits of the quota
      used:
        $ref: "#/definitions/ResourceList"
        description: The used status of the quota
  ProjectScanner:
    type: object
    required:
      - uuid
    properties:
      uuid:
        type: string
        description: The identifier of the scanner registration
  CVEAllowlist:
    type: object
    description: The CVE Allowlist for system or project
    properties:
      id:
        type: integer
        description: ID of the allowlist
      project_id:
        type: integer
        description: ID of the project which the allowlist belongs to.  For system level allowlist this attribute is zero.
      expires_at:
        type: integer
        description: the time for expiration of the allowlist, in the form of seconds since epoch.  This is an optional attribute, if it's not set the CVE allowlist does not expire.
        x-nullable: true
      items:
        type: array
        items:
          $ref: "#/definitions/CVEAllowlistItem"
      creation_time:
        type: string
        format: date-time
        description: The creation time of the allowlist.
      update_time:
        type: string
        format: date-time
        description: The update time of the allowlist.
  CVEAllowlistItem:
    type: object
    description: The item in CVE allowlist
    properties:
      cve_id:
        type: string
        description: The ID of the CVE, such as "CVE-2019-10164"
  ReplicationPolicy:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: The policy ID.
      name:
        type: string
        description: The policy name.
      description:
        type: string
        description: The description of the policy.
      src_registry:
        description: The source registry.
        $ref: '#/definitions/Registry'
      dest_registry:
        description: The destination registry.
        $ref: '#/definitions/Registry'
      dest_namespace:
        type: string
        description: The destination namespace.
      dest_namespace_replace_count:
        type: integer
        format: int8
        description: |-
          Specify how many path components will be replaced by the provided destination namespace.
          The default value is -1 in which case the legacy mode will be applied.
        x-isnullable: true # make this field optional to keep backward compatibility
      trigger:
        $ref: '#/definitions/ReplicationTrigger'
      filters:
        type: array
        description: The replication policy filter array.
        items:
          $ref: '#/definitions/ReplicationFilter'
      replicate_deletion:
        type: boolean
        description: Whether to replicate the deletion operation.
      deletion:
        type: boolean
        description: Deprecated, use "replicate_deletion" instead. Whether to replicate the deletion operation.
      override:
        type: boolean
        description: Whether to override the resources on the destination registry.
      enabled:
        type: boolean
        description: Whether the policy is enabled or not.
      creation_time:
        type: string
        format: date-time
        description: The create time of the policy.
      update_time:
        type: string
        format: date-time
        description: The update time of the policy.
      speed:
        type: integer
        format: int32
        description: speed limit for each task
        x-isnullable: true # make this field optional to keep backward compatibility
      copy_by_chunk:
        type: boolean
        description: Whether to enable copy by chunk.
        x-isnullable: true
  ReplicationTrigger:
    type: object
    properties:
      type:
        type: string
        description: 'The replication policy trigger type. The valid values are manual, event_based and scheduled.'
      trigger_settings:
        $ref: '#/definitions/ReplicationTriggerSettings'
  ReplicationTriggerSettings:
    type: object
    properties:
      cron:
        type: string
        description: The cron string for scheduled trigger
  ReplicationFilter:
    type: object
    properties:
      type:
        type: string
        description: 'The replication policy filter type.'
      value:
        type: object
        description: 'The value of replication policy filter.'
      decoration:
        type: string
        description: 'matches or excludes the result'
  RegistryCredential:
    type: object
    properties:
      type:
        type: string
        description: Credential type, such as 'basic', 'oauth'.
      access_key:
        type: string
        description: Access key, e.g. user name when credential type is 'basic'.
      access_secret:
        type: string
        description: Access secret, e.g. password when credential type is 'basic'.
  Registry:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: The registry ID.
        x-omitempty: false
      url:
        type: string
        description: The registry URL string.
      name:
        type: string
        description: The registry name.
      credential:
        $ref: '#/definitions/RegistryCredential'
      type:
        type: string
        description: Type of the registry, e.g. 'harbor'.
      insecure:
        type: boolean
        description: Whether or not the certificate will be verified when Harbor tries to access the server.
      description:
        type: string
        description: Description of the registry.
      status:
        type: string
        description: Health status of the registry.
      creation_time:
        type: string
        format: date-time
        description: The create time of the policy.
      update_time:
        type: string
        format: date-time
        description: The update time of the policy.
  RegistryUpdate:
    type: object
    properties:
      name:
        type: string
        description: The registry name.
        x-nullable: true
      description:
        type: string
        description: Description of the registry.
        x-nullable: true
      url:
        type: string
        description: The registry URL.
        x-nullable: true
      credential_type:
        type: string
        description: Credential type of the registry, e.g. 'basic'.
        x-nullable: true
      access_key:
        type: string
        description: The registry access key.
        x-nullable: true
      access_secret:
        type: string
        description: The registry access secret.
        x-nullable: true
      insecure:
        type: boolean
        description: Whether or not the certificate will be verified when Harbor tries to access the server.
        x-nullable: true
  RegistryPing:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: The registry ID.
        x-nullable: true
      type:
        type: string
        description: Type of the registry, e.g. 'harbor'.
        x-nullable: true
      url:
        type: string
        description: The registry URL.
        x-nullable: true
      credential_type:
        type: string
        description: Credential type of the registry, e.g. 'basic'.
        x-nullable: true
      access_key:
        type: string
        description: The registry access key.
        x-nullable: true
      access_secret:
        type: string
        description: The registry access secret.
        x-nullable: true
      insecure:
        type: boolean
        description: Whether or not the certificate will be verified when Harbor tries to access the server.
        x-nullable: true
  RegistryInfo:
    type: object
    description: The registry info contains the base info and capability declarations of the registry
    properties:
      type:
        type: string
        description: The registry type
      description:
        type: string
        description: The description
      supported_resource_filters:
        type: array
        description: The filters that the registry supports
        items:
          $ref: '#/definitions/FilterStyle'
      supported_triggers:
        type: array
        description: The triggers that the registry supports
        items:
          type: string
      supported_copy_by_chunk:
        type: boolean
        description: The registry whether support copy by chunk.
        x-omitempty: true
        x-isnullable: true
  RegistryProviderInfo:
    type: object
    description: The registry provider info contains the base info and capability declarations of the registry provider
    properties:
      endpoint_pattern:
        description: The endpoint pattern
        $ref: '#/definitions/RegistryProviderEndpointPattern'
      credential_pattern:
        description: The credential pattern
        $ref: '#/definitions/RegistryProviderCredentialPattern'
  RegistryProviderEndpointPattern:
    type: object
    description: The registry endpoint pattern
    properties:
      endpoint_type:
        type: string
        description: The endpoint type
      endpoints:
        type: array
        description: The endpoint list
        items:
          $ref: '#/definitions/RegistryEndpoint'
  RegistryProviderCredentialPattern:
    type: object
    description: The registry credential pattern
    properties:
      access_key_type:
        type: string
        description: The access key type
      access_key_data:
        type: string
        description: The access key data
      access_secret_type:
        type: string
        description: The access secret type
      access_secret_data:
        type: string
        description: The access secret data
  RegistryEndpoint:
    type: object
    description: The style of the resource filter
    properties:
      key:
        type: string
        description: The endpoint key
      value:
        type: string
        description: The endpoint value
  FilterStyle:
    type: object
    description: The style of the resource filter
    properties:
      type:
        type: string
        description: The filter type
      style:
        type: string
        description: The filter style
      values:
        type: array
        description: The filter values
        items:
          type: string
  ResourceList:
    type: object
    additionalProperties:
      type: integer
      format: int64
  ReplicationExecution:
    type: object
    description: The replication execution
    properties:
      id:
        type: integer
        description: The ID of the execution
      policy_id:
        type: integer
        description: The ID if the policy that the execution belongs to
      status:
        type: string
        description: The status of the execution
      trigger:
        type: string
        description: The trigger mode
      start_time:
        type: string
        format: date-time
        description: The start time
      end_time:
        type: string
        format: date-time
        description: The end time
      status_text:
        type: string
        x-omitempty: false
        description: The status text
      total:
        type: integer
        x-omitempty: false
        description: The total count of all executions
      failed:
        type: integer
        x-omitempty: false
        description: The count of failed executions
      succeed:
        type: integer
        x-omitempty: false
        description: The count of succeed executions
      in_progress:
        type: integer
        x-omitempty: false
        description: The count of in_progress executions
      stopped:
        type: integer
        x-omitempty: false
        description: The count of stopped executions
  StartReplicationExecution:
    type: object
    properties:
      policy_id:
        type: integer
        format: int64
        description: The ID of policy that the execution belongs to.
  ReplicationTask:
    type: object
    description: The replication task
    properties:
      id:
        type: integer
        description: The ID of the task
      execution_id:
        type: integer
        description: The ID of the execution that the task belongs to
      status:
        type: string
        description: The status of the task
      job_id:
        type: string
        description: The ID of the underlying job that the task related to
      operation:
        type: string
        description: The operation of the task
      resource_type:
        type: string
        description: The type of the resource that the task operates
      src_resource:
        type: string
        description: The source resource that the task operates
      dst_resource:
        type: string
        description: The destination resource that the task operates
      start_time:
        type: string
        format: date-time
        description: The start time of the task
      end_time:
        type: string
        format: date-time
        description: The end time of the task
  Robot:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: The ID of the robot
      name:
        type: string
        description: The name of the robot
      description:
        type: string
        description: The description of the robot
      secret:
        type: string
        description: The secret of the robot
      level:
        type: string
        description: The level of the robot, project or system
      duration:
        type: integer
        x-nullable: true
        format: int64
        description: The duration of the robot in days, duration must be either -1(Never) or a positive integer
      editable:
        type: boolean
        x-omitempty: false
        description: The editable status of the robot
      disable:
        type: boolean
        x-omitempty: false
        description: The disable status of the robot
      expires_at:
        type: integer
        format: int64
        description: The expiration date of the robot
      permissions:
        type: array
        items:
          $ref: '#/definitions/RobotPermission'
      creator_type:
        type: string
        description: The type of the robot creator, like local(harbor_user) or robot.
      creator_ref:
        type: integer
        description: The reference of the robot creator, like the id of harbor user.
      creation_time:
        type: string
        format: date-time
        description: The creation time of the robot.
      update_time:
        type: string
        format: date-time
        description: The update time of the robot.
  RobotCreate:
    type: object
    description: The request for robot account creation.
    properties:
      name:
        type: string
        description: The name of the robot
      description:
        type: string
        description: The description of the robot
      secret:
        type: string
        description: The secret of the robot
      level:
        type: string
        description: The level of the robot, project or system
      disable:
        type: boolean
        description: The disable status of the robot
      duration:
        type: integer
        format: int64
        description: The duration of the robot in days, duration must be either -1(Never) or a positive integer
      permissions:
        type: array
        items:
          $ref: '#/definitions/RobotPermission'
  RobotCreated:
    type: object
    description: The response for robot account creation.
    properties:
      id:
        type: integer
        format: int64
        description: The ID of the robot
      name:
        type: string
        description: The name of the robot
      secret:
        type: string
        description: The secret of the robot
      creation_time:
        type: string
        format: date-time
        description: The creation time of the robot.
      expires_at:
        type: integer
        format: int64
        description: The expiration date of the robot
  RobotSec:
    type: object
    description: The response for refresh/update robot account secret.
    properties:
      secret:
        type: string
        description: The secret of the robot
  RobotPermission:
    type: object
    properties:
      kind:
        type: string
        description: The kind of the permission
      namespace:
        type: string
        description: The namespace of the permission
      access:
        type: array
        items:
          $ref: '#/definitions/Access'
  Access:
    type: object
    properties:
      resource:
        type: string
        description: The resource of the access. Possible resources are listed here for system and project level https://github.com/goharbor/harbor/blob/main/src/common/rbac/const.go 
      action:
        type: string
        description: The action of the access. Possible actions are *, pull, push, create, read, update, delete, list, operate, scanner-pull and stop.
      effect:
        type: string
        description: The effect of the access
  RobotCreateV1:
    type: object
    properties:
      name:
        type: string
        description: The name of robot account
      description:
        type: string
        description: The description of robot account
      expires_at:
        type: integer
        description: The expiration time on or after which the JWT MUST NOT be accepted for processing.
      access:
        type: array
        description: The permission of robot account
        items:
          $ref: '#/definitions/Access'
  Storage:
    type: object
    properties:
      total:
        type: integer
        format: uint64
        description: Total volume size.
      free:
        type: integer
        format: uint64
        description: Free volume size.
  GeneralInfo:
    type: object
    properties:
      banner_message:
        type: string
        x-nullable: true
        x-omitempty: true
        description: The banner message for the UI. It is the stringified result of the banner message object.
        example: "{\"closable\":true,\"message\":\"your banner message content\",\"type\":\"warning\",\"fromDate\":\"06/19/2023\",\"toDate\":\"06/21/2023\"}"
      current_time:
        type: string
        format: date-time
        x-nullable: true
        x-omitempty: true
        description: The current time of the server.
      registry_url:
        type: string
        x-nullable: true
        x-omitempty: true
        description: The url of registry against which the docker command should be issued.
      external_url:
        type: string
        x-nullable: true
        x-omitempty: true
        description: The external URL of Harbor, with protocol.
      auth_mode:
        type: string
        x-nullable: true
        x-omitempty: true
        description: The auth mode of current Harbor instance.
      primary_auth_mode:
        type: boolean
        x-nullable: true
        x-omitempty: true
        description: The flag to indicate whether the current auth mode should consider as a primary one.
      project_creation_restriction:
        type: string
        x-nullable: true
        x-omitempty: true
        description: 'Indicate who can create projects, it could be ''adminonly'' or ''everyone''.'
      self_registration:
        type: boolean
        x-nullable: true
        x-omitempty: true
        description: Indicate whether the Harbor instance enable user to register himself.
      has_ca_root:
        type: boolean
        x-nullable: true
        x-omitempty: true
        description: Indicate whether there is a ca root cert file ready for download in the file system.
      harbor_version:
        type: string
        x-nullable: true
        x-omitempty: true
        description: The build version of Harbor.
      registry_storage_provider_name:
        type: string
        x-nullable: true
        x-omitempty: true
        description: The storage provider's name of Harbor registry
      read_only:
        type: boolean
        x-nullable: true
        x-omitempty: true
        description: The flag to indicate whether Harbor is in readonly mode.
      notification_enable:
        type: boolean
        x-nullable: true
        x-omitempty: true
        description: The flag to indicate whether notification mechanism is enabled on Harbor instance.
      authproxy_settings:
        description: The setting of auth proxy this is only available when Harbor relies on authproxy for authentication.
        x-nullable: true
        x-omitempty: true
        $ref: '#/definitions/AuthproxySetting'
      oidc_provider_name:
        type: string
        x-nullable: true
        x-omitempty: true
        description: The OIDC provider name, empty if current auth is not OIDC_auth or OIDC provider is not configured.
  AuthproxySetting:
    type: object
    properties:
      endpoint:
        type: string
        description: The fully qualified URI of login endpoint of authproxy, such as 'https://***********:8443/login'
      tokenreivew_endpoint:
        type: string
        description: The fully qualified URI of token review endpoint of authproxy, such as 'https://***********:8443/tokenreview'
      skip_search:
        type: boolean
        description: The flag to determine whether Harbor can skip search the user/group when adding him as a member.
      verify_cert:
        type: boolean
        description: The flag to determine whether Harbor should verify the certificate when connecting to the auth proxy.
      server_certificate:
        type: string
        description: The certificate to be pinned when connecting auth proxy.
  SystemInfo:
    type: object
    properties:
      storage:
        type: array
        description: The storage of system.
        items:
          $ref: '#/definitions/Storage'
  GCHistory:
    type: object
    properties:
      id:
        type: integer
        description: the id of gc job.
      job_name:
        type: string
        description: the job name of gc job.
      job_kind:
        type: string
        description: the job kind of gc job.
      job_parameters:
        type: string
        description: the job parameters of gc job.
      schedule:
        $ref: '#/definitions/ScheduleObj'
      job_status:
        type: string
        description: the status of gc job.
      deleted:
        type: boolean
        description: if gc job was deleted.
      creation_time:
        type: string
        format: date-time
        description: the creation time of gc job.
      update_time:
        type: string
        format: date-time
        description: the update time of gc job.
  ExecHistory:
    type: object
    properties:
      id:
        type: integer
        description: the id of purge job.
      job_name:
        type: string
        description: the job name of purge job.
      job_kind:
        type: string
        description: the job kind of purge job.
      job_parameters:
        type: string
        description: the job parameters of purge job.
      schedule:
        $ref: '#/definitions/ScheduleObj'
      job_status:
        type: string
        description: the status of purge job.
      deleted:
        type: boolean
        description: if purge job was deleted.
      creation_time:
        type: string
        format: date-time
        description: the creation time of purge job.
      update_time:
        type: string
        format: date-time
        description: the update time of purge job.
  Schedule:
    type: object
    properties:
      id:
        type: integer
        description: The id of the schedule.
        readOnly: true
      status:
        type: string
        description: The status of the schedule.
        readOnly: true
      creation_time:
        type: string
        format: date-time
        description: the creation time of the schedule.
        readOnly: true
      update_time:
        type: string
        format: date-time
        description: the update time of the schedule.
        readOnly: true
      schedule:
        $ref: '#/definitions/ScheduleObj'
      parameters:
        type: object
        description: The parameters of schedule job
        additionalProperties:
          type: object
  ScheduleObj:
    type: object
    properties:
      type:
        type: string
        description: |
          The schedule type. The valid values are 'Hourly', 'Daily', 'Weekly', 'Custom', 'Manual', 'None' and 'Schedule'.
          'Manual' means to trigger it right away, 'Schedule' means to trigger it by a specified cron schedule and
          'None' means to cancel the schedule.
        enum:
          - Hourly
          - Daily
          - Weekly
          - Custom
          - Manual
          - None
          - Schedule
      cron:
        type: string
        description: A cron expression, a time-based job scheduler.
      next_scheduled_time:
        type: string
        format: date-time
        description: The next time to schedule to run the job.
  Stats:
    type: object
    description: Stats provides the overall progress of the scan all process.
    properties:
      total:
        type: integer
        format: int
        description: 'The total number of scan processes triggered by the scan all action'
        example: 100
        x-omitempty: false
      completed:
        type: integer
        format: int
        description: 'The number of the finished scan processes triggered by the scan all action'
        example: 90
        x-omitempty: false
      metrics:
        type: object
        description: 'The metrics data for the each status'
        additionalProperties:
          type: integer
          format: int
          example: 10
        example:
          'Success': 5
          'Error': 2
          'Running': 3
      ongoing:
        type: boolean
        description: A flag indicating job status of scan all.
        x-omitempty: false
      trigger:
        type: string
        description: The trigger of the scan all job.
        enum:
          - Manual
          - Schedule
          - Event
  RetentionMetadata:
    type: object
    description: the tag retention metadata
    properties:
      templates:
        type: array
        description: templates
        items:
          $ref: '#/definitions/RetentionRuleMetadata'
      scope_selectors:
        type: array
        description: supported scope selectors
        items:
          $ref: '#/definitions/RetentionSelectorMetadata'
      tag_selectors:
        type: array
        description: supported tag selectors
        items:
          $ref: '#/definitions/RetentionSelectorMetadata'

  RetentionRuleMetadata:
    type: object
    description: the tag retention rule metadata
    properties:
      rule_template:
        type: string
        description: rule id
      display_text:
        type: string
        description: rule display text
      action:
        type: string
        description: rule action
      params:
        type: array
        description: rule params
        items:
          $ref: '#/definitions/RetentionRuleParamMetadata'

  RetentionRuleParamMetadata:
    type: object
    description: rule param
    properties:
      type:
        type: string
      unit:
        type: string
      required:
        type: boolean


  RetentionSelectorMetadata:
    type: object
    description: retention selector
    properties:
      display_text:
        type: string
      kind:
        type: string
      decorations:
        type: array
        items:
          type: string

  RetentionPolicy:
    type: object
    description: retention policy
    properties:
      id:
        type: integer
        format: int64
      algorithm:
        type: string
      rules:
        type: array
        items:
          $ref: '#/definitions/RetentionRule'
      trigger:
        type: object
        $ref: '#/definitions/RetentionRuleTrigger'
      scope:
        type: object
        $ref: '#/definitions/RetentionPolicyScope'

  RetentionRuleTrigger:
    type: object
    properties:
      kind:
        type: string
      settings:
        type: object
      references:
        type: object

  RetentionPolicyScope:
    type: object
    properties:
      level:
        type: string
      ref:
        type: integer

  RetentionRule:
    type: object
    properties:
      id:
        type: integer
      priority:
        type: integer
      disabled:
        type: boolean
      action:
        type: string
      template:
        type: string
      params:
        type: object
        additionalProperties:
          type: object
      tag_selectors:
        type: array
        items:
          $ref: '#/definitions/RetentionSelector'
      scope_selectors:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/RetentionSelector'

  RetentionSelector:
    type: object
    properties:
      kind:
        type: string
      decoration:
        type: string
      pattern:
        type: string
      extras:
        type: string

  RetentionExecution:
    type: object
    properties:
      id:
        type: integer
        format: int64
      policy_id:
        type: integer
        format: int64
      start_time:
        type: string
      end_time:
        type: string
      status:
        type: string
      trigger:
        type: string
      dry_run:
        type: boolean

  RetentionExecutionTask:
    type: object
    properties:
      id:
        type: integer
        format: int64
      execution_id:
        type: integer
        format: int64
      repository:
        type: string
      job_id:
        type: string
      status:
        type: string
      status_code:
        type: integer
        x-omitempty: false
      status_revision:
        type: integer
        format: int64
      start_time:
        type: string
      end_time:
        type: string
      total:
        type: integer
        x-omitempty: false
      retained:
        type: integer
        x-omitempty: false

  QuotaUpdateReq:
    type: object
    properties:
      hard:
        $ref: "#/definitions/ResourceList"
        description: The new hard limits for the quota

  QuotaRefObject:
    type: object
    additionalProperties: {}

  Quota:
    type: object
    description: The quota object
    properties:
      id:
        type: integer
        description: ID of the quota
      ref:
        $ref: "#/definitions/QuotaRefObject"
        description: The reference object of the quota
      hard:
        $ref: "#/definitions/ResourceList"
        description: The hard limits of the quota
        x-omitempty: false
      used:
        $ref: "#/definitions/ResourceList"
        description: The used status of the quota
        x-omitempty: false
      creation_time:
        type: string
        format: date-time
        description: the creation time of the quota
      update_time:
        type: string
        format: date-time
        description: the update time of the quota

  ScannerRegistration:
    type: object
    description: |
      Registration represents a named configuration for invoking a scanner via its adapter.
    properties:
      uuid:
        type: string
        description: The unique identifier of this registration.
      name:
        type: string
        example: Trivy
        description: The name of this registration.
      description:
        type: string
        description: An optional description of this registration.
        example: |
          A free-to-use tool that scans container images for package vulnerabilities.
        x-omitempty: false
      url:
        type: string
        format: uri
        description: A base URL of the scanner adapter
        example: http://harbor-scanner-trivy:8080
      disabled:
        type: boolean
        default: false
        description: Indicate whether the registration is enabled or not
        x-omitempty: false
      is_default:
        type: boolean
        default: false
        description: Indicate if the registration is set as the system default one
        x-omitempty: false
      auth:
        type: string
        default: ""
        description: |
          Specify what authentication approach is adopted for the HTTP communications.
          Supported types Basic", "Bearer" and api key header "X-ScannerAdapter-API-Key"
        example: "Bearer"
        x-omitempty: false
      access_credential:
        type: string
        description: |
          An optional value of the HTTP Authorization header sent with each request to the Scanner Adapter API.
        example: "Bearer: JWTTOKENGOESHERE"
        x-omitempty: false
      skip_certVerify:
        type: boolean
        default: false
        description: Indicate if skip the certificate verification when sending HTTP requests
        x-omitempty: false
      use_internal_addr:
        type: boolean
        default: false
        description: Indicate whether use internal registry addr for the scanner to pull content or not
        x-omitempty: false
      create_time:
        type: string
        format: date-time
        description: The creation time of this registration
      update_time:
        type: string
        format: date-time
        description: The update time of this registration
      adapter:
        type: string
        description: Optional property to describe the name of the scanner registration
        example: "Trivy"
      vendor:
        type: string
        description: Optional property to describe the vendor of the scanner registration
        example: "CentOS"
      version:
        type: string
        description: Optional property to describe the version of the scanner registration
        example: "1.0.1"
      health:
        type: string
        default: ""
        description: Indicate the healthy of the registration
        example: "healthy"
      capabilities:
        type: object
        description: Indicates the capabilities of the scanner, e.g. support_vulnerability or support_sbom.
        additionalProperties: True
        example:  {"support_vulnerability": true, "support_sbom": true}

  ScannerRegistrationReq:
    type: object
    required:
      - name
      - url
    properties:
      name:
        type: string
        description: The name of this registration
        example: Trivy
      description:
        type: string
        description: An optional description of this registration.
        example: |
          A free-to-use tool that scans container images for package vulnerabilities.
      url:
        type: string
        format: uri
        description: A base URL of the scanner adapter.
        example: http://harbor-scanner-trivy:8080
      auth:
        type: string
        description: |
          Specify what authentication approach is adopted for the HTTP communications.
          Supported types Basic", "Bearer" and api key header "X-ScannerAdapter-API-Key"
        example: "Bearer"
      access_credential:
        type: string
        description: |
          An optional value of the HTTP Authorization header sent with each request to the Scanner Adapter API.
        example: "Bearer: JWTTOKENGOESHERE"
      skip_certVerify:
        type: boolean
        default: false
        description: Indicate if skip the certificate verification when sending HTTP requests
      use_internal_addr:
        type: boolean
        default: false
        description: Indicate whether use internal registry addr for the scanner to pull content or not
      disabled:
        type: boolean
        default: false
        description: Indicate whether the registration is enabled or not

  ScannerRegistrationSettings:
    type: object
    required:
      - name
      - url
    properties:
      name:
        type: string
        description: The name of this registration
        example: Trivy
      url:
        type: string
        format: uri
        description: A base URL of the scanner adapter.
        example: http://harbor-scanner-trivy:8080
      auth:
        type: string
        default: ""
        description: |
          Specify what authentication approach is adopted for the HTTP communications.
          Supported types Basic", "Bearer" and api key header "X-ScannerAdapter-API-Key"
      access_credential:
        type: string
        description: |
          An optional value of the HTTP Authorization header sent with each request to the Scanner Adapter API.
        example: "Bearer: JWTTOKENGOESHERE"

  IsDefault:
    type: object
    properties:
      is_default:
        type: boolean
        description: A flag indicating whether a scanner registration is default.

  ScannerCapability:
    type: object
    properties:
      type:
        type: string
        description: |
          Specify the type of scanner capability, like vulnerability or sbom
        x-omitempty: false
        example: "sbom"
      consumes_mime_types:
        type: array
        items:
          type: string
          example: "application/vnd.docker.distribution.manifest.v2+json"
      produces_mime_types:
        type: array
        items:
          type: string
          example: "application/vnd.scanner.adapter.vuln.report.harbor+json; version=1.0"

  ScannerAdapterMetadata:
    type: object
    description: The metadata info of the scanner adapter
    properties:
      scanner:
        $ref: '#/definitions/Scanner'
      capabilities:
        type: array
        items:
          $ref: '#/definitions/ScannerCapability'
      properties:
        type: object
        additionalProperties:
          type: string
        example:
          'harbor.scanner-adapter/registry-authorization-type': 'Bearer'

  ImmutableRule:
    type: object
    properties:
      id:
        type: integer
      priority:
        type: integer
      disabled:
        type: boolean
      action:
        type: string
      template:
        type: string
      params:
        type: object
        additionalProperties:
          type: object
      tag_selectors:
        type: array
        items:
          $ref: '#/definitions/ImmutableSelector'
      scope_selectors:
        type: object
        additionalProperties:
          type: array
          items:
            $ref: '#/definitions/ImmutableSelector'
  ImmutableSelector:
    type: object
    properties:
      kind:
        type: string
      decoration:
        type: string
      pattern:
        type: string
      extras:
        type: string
  LdapConf:
    type: object
    description: The ldap configure properties
    properties:
      ldap_url:
        type: string
        description: The url of ldap service.
      ldap_search_dn:
        type: string
        description: The search dn of ldap service.
      ldap_search_password:
        type: string
        description: The search password of ldap service.
      ldap_base_dn:
        type: string
        description: The base dn of ldap service.
      ldap_filter:
        type: string
        description: The serach filter of ldap service.
      ldap_uid:
        type: string
        description: The serach uid from ldap service attributes.
      ldap_scope:
        type: integer
        format: int64
        description: The serach scope of ldap service.
      ldap_connection_timeout:
        type: integer
        format: int64
        description: The connect timeout of ldap service(second).
      ldap_verify_cert:
        type: boolean
        description: Verify Ldap server certificate.
  LdapPingResult:
    type: object
    description: The ldap ping result
    properties:
      success:
        type: boolean
        description: Test success
      message:
        type: string
        description: The ping operation output message.
  LdapImportUsers:
    type: object
    properties:
      ldap_uid_list:
        type: array
        description: selected uid list
        items:
          type: string
  LdapFailedImportUser:
    type: object
    properties:
      uid:
        type: string
        description: the uid can't add to system.
      error:
        type: string
        description: fail reason.
  LdapUser:
    type: object
    properties:
      username:
        type: string
        description: ldap username.
      realname:
        type: string
        description: The user realname from "uid" or "cn" attribute.
      email:
        type: string
        description: The user email address from "mail" or "email" attribute.
  UserGroup:
    type: object
    properties:
      id:
        type: integer
        description: The ID of the user group
      group_name:
        type: string
        description: The name of the user group
      group_type:
        type: integer
        description: 'The group type, 1 for LDAP group, 2 for HTTP group, 3 for OIDC group.'
      ldap_group_dn:
        type: string
        description: The DN of the LDAP group if group type is 1 (LDAP group).
  UserGroupSearchItem:
    type: object
    properties:
      id:
        type: integer
        description: The ID of the user group
      group_name:
        type: string
        description: The name of the user group
      group_type:
        type: integer
        description: 'The group type, 1 for LDAP group, 2 for HTTP group, 3 for OIDC group.'
  SupportedWebhookEventTypes:
    type: object
    description: Supported webhook event types and notify types.
    properties:
      event_type:
        type: array
        items:
          $ref: '#/definitions/EventType'
      notify_type:
        type: array
        items:
          $ref: '#/definitions/NotifyType'
      payload_formats:
        type: array
        items:
          $ref: '#/definitions/PayloadFormat'
  EventType:
    type: string
    description: Webhook supported event type.
    example: 'PULL_ARTIFACT'
  NotifyType:
    type: string
    description: Webhook supported notify type.
    example: 'http'
  PayloadFormatType:
    type: string
    description: The type of webhook paylod format.
    example: 'CloudEvents'
  PayloadFormat:
    type: object
    description: Webhook supported payload format type collections.
    properties:
      notify_type:
        $ref: '#/definitions/NotifyType'
      formats:
        type: array
        description: The supported payload formats for this notify type.
        items:
          $ref: '#/definitions/PayloadFormatType'

  WebhookTargetObject:
    type: object
    description: The webhook policy target object.
    properties:
      type:
        type: string
        description: The webhook target notify type.
      address:
        type: string
        description: The webhook target address.
      auth_header:
        type: string
        description: The webhook auth header.
      skip_cert_verify:
        type: boolean
        description: Whether or not to skip cert verify.
      payload_format:
        $ref: '#/definitions/PayloadFormatType'
        description: The payload format of webhook, by default is Default for http type.
  WebhookPolicy:
    type: object
    description: The webhook policy object
    properties:
      id:
        type: integer
        format: int64
        description: The webhook policy ID.
      name:
        type: string
        description: The name of webhook policy.
      description:
        type: string
        description: The description of webhook policy.
      project_id:
        type: integer
        description: The project ID of webhook policy.
      targets:
        type: array
        items:
          $ref: '#/definitions/WebhookTargetObject'
      event_types:
        type: array
        items:
          type: string
      creator:
        type: string
        description: The creator of the webhook policy.
      creation_time:
        type: string
        description: The create time of the webhook policy.
        format: date-time
      update_time:
        type: string
        description: The update time of the webhook policy.
        format: date-time
      enabled:
        type: boolean
        description: Whether the webhook policy is enabled or not.
        x-omitempty: false
  WebhookLastTrigger:
    type: object
    description: The webhook policy and last trigger time group by event type.
    properties:
      policy_name:
        type: string
        description: The webhook policy name.
      event_type:
        type: string
        description: The webhook event type.
      enabled:
        type: boolean
        description: Whether or not the webhook policy enabled.
      creation_time:
        type: string
        description: The creation time of webhook policy.
        format: date-time
      last_trigger_time:
        type: string
        description: The last trigger time of webhook policy.
        format: date-time
  WebhookJob:
    type: object
    description: The webhook job.
    properties:
      id:
        type: integer
        format: int64
        description: The webhook job ID.
      policy_id:
        type: integer
        format: int64
        description: The webhook policy ID.
      event_type:
        type: string
        description: The webhook job event type.
      notify_type:
        type: string
        description: The webhook job notify type.
      status:
        type: string
        description: The webhook job status.
      job_detail:
        type: string
        description: The webhook job notify detailed data.
      creation_time:
        type: string
        description: The webhook job creation time.
        format: date-time
      update_time:
        type: string
        description: The webhook job update time.
        format: date-time
  InternalConfigurationsResponse:
    type: object
    additionalProperties:
      $ref: '#/definitions/InternalConfigurationValue'
  InternalConfigurationValue:
    type: object
    properties:
      value:
        type: object
        description: The value of current config item
      editable:
        type: boolean
        x-omitempty: false
        description: The configure item can be updated or not
  ConfigurationsResponse:
    type: object
    properties:
      auth_mode:
        $ref: '#/definitions/StringConfigItem'
        description: The auth mode of current system, such as "db_auth", "ldap_auth", "oidc_auth"
      primary_auth_mode:
        $ref: '#/definitions/BoolConfigItem'
        description: The flag to indicate whether the current auth mode should consider as a primary one.
      ldap_base_dn:
        $ref: '#/definitions/StringConfigItem'
        description: The Base DN for LDAP binding.
      ldap_filter:
        $ref: '#/definitions/StringConfigItem'
        description: The filter for LDAP search
      ldap_group_base_dn:
        $ref: '#/definitions/StringConfigItem'
        description: The base DN to search LDAP group.
      ldap_group_admin_dn:
        $ref: '#/definitions/StringConfigItem'
        description: Specify the ldap group which have the same privilege with Harbor admin
      ldap_group_attribute_name:
        $ref: '#/definitions/StringConfigItem'
        description: The attribute which is used as identity of the LDAP group, default is cn.'
      ldap_group_search_filter:
        $ref: '#/definitions/StringConfigItem'
        description: The filter to search the ldap group
      ldap_group_search_scope:
        $ref: '#/definitions/IntegerConfigItem'
        description: The scope to search ldap group. ''0-LDAP_SCOPE_BASE, 1-LDAP_SCOPE_ONELEVEL, 2-LDAP_SCOPE_SUBTREE''
      ldap_group_attach_parallel:
        $ref: '#/definitions/BoolConfigItem'
        description: Attach LDAP user group information in parallel.
      ldap_scope:
        $ref: '#/definitions/IntegerConfigItem'
        description: The scope to search ldap users,'0-LDAP_SCOPE_BASE, 1-LDAP_SCOPE_ONELEVEL, 2-LDAP_SCOPE_SUBTREE'
      ldap_search_dn:
        $ref: '#/definitions/StringConfigItem'
        description: The DN of the user to do the search.
      ldap_timeout:
        $ref: '#/definitions/IntegerConfigItem'
        description: Timeout in seconds for connection to LDAP server
      ldap_uid:
        $ref: '#/definitions/StringConfigItem'
        description: The attribute which is used as identity for the LDAP binding, such as "CN" or "SAMAccountname"
      ldap_url:
        $ref: '#/definitions/StringConfigItem'
        description: The URL of LDAP server
      ldap_verify_cert:
        $ref: '#/definitions/BoolConfigItem'
        description: Whether verify your OIDC server certificate, disable it if your OIDC server is hosted via self-hosted certificate.
      ldap_group_membership_attribute:
        $ref: '#/definitions/StringConfigItem'
        description: The user attribute to identify the group membership
      project_creation_restriction:
        $ref: '#/definitions/StringConfigItem'
        description: Indicate who can create projects, it could be ''adminonly'' or ''everyone''.
      read_only:
        $ref: '#/definitions/BoolConfigItem'
        description: The flag to indicate whether Harbor is in readonly mode.
      self_registration:
        $ref: '#/definitions/BoolConfigItem'
        description: Whether the Harbor instance supports self-registration.  If it''s set to false, admin need to add user to the instance.
      token_expiration:
        $ref: '#/definitions/IntegerConfigItem'
        description: The expiration time of the token for internal Registry, in minutes.
      uaa_client_id:
        $ref: '#/definitions/StringConfigItem'
        description: The client id of UAA
      uaa_client_secret:
        $ref: '#/definitions/StringConfigItem'
        description: The client secret of the UAA
      uaa_endpoint:
        $ref: '#/definitions/StringConfigItem'
        description: The endpoint of the UAA
      uaa_verify_cert:
        $ref: '#/definitions/BoolConfigItem'
        description: Verify the certificate in UAA server
      http_authproxy_endpoint:
        $ref: '#/definitions/StringConfigItem'
        description: The endpoint of the HTTP auth
      http_authproxy_tokenreview_endpoint:
        $ref: '#/definitions/StringConfigItem'
        description: The token review endpoint
      http_authproxy_admin_groups:
        $ref: '#/definitions/StringConfigItem'
        description: The group which has the harbor admin privileges
      http_authproxy_admin_usernames:
        $ref: '#/definitions/StringConfigItem'
        description: The usernames which has the harbor admin privileges
      http_authproxy_verify_cert:
        $ref: '#/definitions/BoolConfigItem'
        description: Verify the HTTP auth provider's certificate
      http_authproxy_skip_search:
        $ref: '#/definitions/BoolConfigItem'
        description: Search user before onboard
      http_authproxy_server_certificate:
        $ref: '#/definitions/StringConfigItem'
        description: The certificate of the HTTP auth provider
      oidc_name:
        $ref: '#/definitions/StringConfigItem'
        description: The OIDC provider name
      oidc_endpoint:
        $ref: '#/definitions/StringConfigItem'
        description: The endpoint of the OIDC provider
      oidc_client_id:
        $ref: '#/definitions/StringConfigItem'
        description: The client ID of the OIDC provider
      oidc_groups_claim:
        $ref: '#/definitions/StringConfigItem'
        description: The attribute claims the group name
      oidc_admin_group:
        $ref: '#/definitions/StringConfigItem'
        description: The OIDC group which has the harbor admin privileges
      oidc_group_filter:
        $ref: '#/definitions/StringConfigItem'
        description: The OIDC group filter which filters out the group doesn't match the regular expression
      oidc_scope:
        $ref: '#/definitions/StringConfigItem'
        description: The scope of the OIDC provider
      oidc_user_claim:
        $ref: '#/definitions/StringConfigItem'
        description: The attribute claims the username
      oidc_verify_cert:
        $ref: '#/definitions/BoolConfigItem'
        description: Verify the OIDC provider's certificate'
      oidc_auto_onboard:
        $ref: '#/definitions/BoolConfigItem'
        description: Auto onboard the OIDC user
      oidc_extra_redirect_parms:
        $ref: '#/definitions/StringConfigItem'
        description: Extra parameters to add when redirect request to OIDC provider
      robot_token_duration:
        $ref: '#/definitions/IntegerConfigItem'
        description: The robot account token duration in days
      robot_name_prefix:
        $ref: '#/definitions/StringConfigItem'
        description: The rebot account name prefix
      notification_enable:
        $ref: '#/definitions/BoolConfigItem'
        description: Enable notification
      quota_per_project_enable:
        $ref: '#/definitions/BoolConfigItem'
        description: Enable quota per project
      storage_per_project:
        $ref: '#/definitions/IntegerConfigItem'
        description: The storage quota per project
      audit_log_forward_endpoint:
        $ref: '#/definitions/StringConfigItem'
        description: The endpoint of the audit log forwarder
      skip_audit_log_database:
        $ref: '#/definitions/BoolConfigItem'
        description: Whether skip the audit log in database
      scanner_skip_update_pulltime:
        $ref: '#/definitions/BoolConfigItem'
        description: Whether or not to skip update the pull time for scanner
      scan_all_policy:
        type: object
        properties:
          type:
            type: string
            description: 'The type of scan all policy, currently the valid values are "none" and "daily"'
          parameter:
            type: object
            properties:
              daily_time:
                type: integer
                description: 'The offset in seconds of UTC 0 o''clock, only valid when the policy type is "daily"'
            description: 'The parameters of the policy, the values are dependent on the type of the policy.'
      session_timeout:
        $ref: '#/definitions/IntegerConfigItem'
        description: The session timeout in minutes
      banner_message:
        $ref: '#/definitions/StringConfigItem'
        description: The banner message for the UI.It is the stringified result of the banner message object
  Configurations:
    type: object
    properties:
      auth_mode:
        type: string
        description: The auth mode of current system, such as "db_auth", "ldap_auth", "oidc_auth"
        x-omitempty: true
        x-isnullable: true
      primary_auth_mode:
        type: boolean
        x-nullable: true
        x-omitempty: true
        description: The flag to indicate whether the current auth mode should consider as a primary one.
      ldap_base_dn:
        type: string
        description: The Base DN for LDAP binding.
        x-omitempty: true
        x-isnullable: true
      ldap_filter:
        type: string
        description: The filter for LDAP search
        x-omitempty: true
        x-isnullable: true
      ldap_group_base_dn:
        type: string
        description: The base DN to search LDAP group.
        x-omitempty: true
        x-isnullable: true
      ldap_group_admin_dn:
        type: string
        description: Specify the ldap group which have the same privilege with Harbor admin
        x-omitempty: true
        x-isnullable: true
      ldap_group_attribute_name:
        type: string
        description: The attribute which is used as identity of the LDAP group, default is cn.'
        x-omitempty: true
        x-isnullable: true
      ldap_group_search_filter:
        type: string
        description: The filter to search the ldap group
        x-omitempty: true
        x-isnullable: true
      ldap_group_search_scope:
        type: integer
        description: The scope to search ldap group. ''0-LDAP_SCOPE_BASE, 1-LDAP_SCOPE_ONELEVEL, 2-LDAP_SCOPE_SUBTREE''
        x-omitempty: true
        x-isnullable: true
      ldap_group_attach_parallel:
        type: boolean
        description: Attach LDAP user group information in parallel, the parallel worker count is 5
        x-omitempty: true
        x-isnullable: true
      ldap_scope:
        type: integer
        description: The scope to search ldap users,'0-LDAP_SCOPE_BASE, 1-LDAP_SCOPE_ONELEVEL, 2-LDAP_SCOPE_SUBTREE'
        x-omitempty: true
        x-isnullable: true
      ldap_search_dn:
        type: string
        description: The DN of the user to do the search.
        x-omitempty: true
        x-isnullable: true
      ldap_search_password:
        type: string
        description: The password of the ldap search dn
        x-omitempty: true
        x-isnullable: true
      ldap_timeout:
        type: integer
        description: Timeout in seconds for connection to LDAP server
        x-omitempty: true
        x-isnullable: true
      ldap_uid:
        type: string
        description: The attribute which is used as identity for the LDAP binding, such as "CN" or "SAMAccountname"
        x-omitempty: true
        x-isnullable: true
      ldap_url:
        type: string
        description: The URL of LDAP server
        x-omitempty: true
        x-isnullable: true
      ldap_verify_cert:
        type: boolean
        description: Whether verify your OIDC server certificate, disable it if your OIDC server is hosted via self-hosted certificate.
        x-omitempty: true
        x-isnullable: true
      ldap_group_membership_attribute:
        type: string
        description: The user attribute to identify the group membership
        x-omitempty: true
        x-isnullable: true
      project_creation_restriction:
        type: string
        description: Indicate who can create projects, it could be ''adminonly'' or ''everyone''.
        x-omitempty: true
        x-isnullable: true
      read_only:
        type: boolean
        description: The flag to indicate whether Harbor is in readonly mode.
        x-omitempty: true
        x-isnullable: true
      self_registration:
        type: boolean
        description: Whether the Harbor instance supports self-registration.  If it''s set to false, admin need to add user to the instance.
        x-omitempty: true
        x-isnullable: true
      token_expiration:
        type: integer
        description: The expiration time of the token for internal Registry, in minutes.
        x-omitempty: true
        x-isnullable: true
      uaa_client_id:
        type: string
        description: The client id of UAA
        x-omitempty: true
        x-isnullable: true
      uaa_client_secret:
        type: string
        description: The client secret of the UAA
        x-omitempty: true
        x-isnullable: true
      uaa_endpoint:
        type: string
        description: The endpoint of the UAA
        x-omitempty: true
        x-isnullable: true
      uaa_verify_cert:
        type: boolean
        description: Verify the certificate in UAA server
        x-omitempty: true
        x-isnullable: true
      http_authproxy_endpoint:
        type: string
        description: The endpoint of the HTTP auth
        x-omitempty: true
        x-isnullable: true
      http_authproxy_tokenreview_endpoint:
        type: string
        description: The token review endpoint
        x-omitempty: true
        x-isnullable: true
      http_authproxy_admin_groups:
        type: string
        description: The group which has the harbor admin privileges
        x-omitempty: true
        x-isnullable: true
      http_authproxy_admin_usernames:
        type: string
        description: The username which has the harbor admin privileges
        x-omitempty: true
        x-isnullable: true
      http_authproxy_verify_cert:
        type: boolean
        description: Verify the HTTP auth provider's certificate
        x-omitempty: true
        x-isnullable: true
      http_authproxy_skip_search:
        type: boolean
        description: Search user before onboard
        x-omitempty: true
        x-isnullable: true
      http_authproxy_server_certificate:
        type: string
        description: The certificate of the HTTP auth provider
        x-omitempty: true
        x-isnullable: true
      oidc_name:
        type: string
        description: The OIDC provider name
        x-omitempty: true
        x-isnullable: true
      oidc_endpoint:
        type: string
        description: The endpoint of the OIDC provider
        x-omitempty: true
        x-isnullable: true
      oidc_client_id:
        type: string
        description: The client ID of the OIDC provider
        x-omitempty: true
        x-isnullable: true
      oidc_client_secret:
        type: string
        description: The OIDC provider secret
        x-omitempty: true
        x-isnullable: true
      oidc_groups_claim:
        type: string
        description: The attribute claims the group name
        x-omitempty: true
        x-isnullable: true
      oidc_admin_group:
        type: string
        description: The OIDC group which has the harbor admin privileges
        x-omitempty: true
        x-isnullable: true
      oidc_group_filter:
        type: string
        description: The OIDC group filter which filters out the group name doesn't match the regular expression
        x-omitempty: true
        x-isnullable: true
      oidc_scope:
        type: string
        description: The scope of the OIDC provider
        x-omitempty: true
        x-isnullable: true
      oidc_user_claim:
        type: string
        description: The attribute claims the username
        x-omitempty: true
        x-isnullable: true
      oidc_verify_cert:
        type: boolean
        description: Verify the OIDC provider's certificate'
        x-omitempty: true
        x-isnullable: true
      oidc_auto_onboard:
        type: boolean
        description: Auto onboard the OIDC user
        x-omitempty: true
        x-isnullable: true
      oidc_extra_redirect_parms:
        type: string
        description: Extra parameters to add when redirect request to OIDC provider
        x-omitempty: true
        x-isnullable: true
      robot_token_duration:
        type: integer
        description: The robot account token duration in days
        x-omitempty: true
        x-isnullable: true
      robot_name_prefix:
        type: string
        description: The rebot account name prefix
        x-omitempty: true
        x-isnullable: true
      notification_enable:
        type: boolean
        description: Enable notification
        x-omitempty: true
        x-isnullable: true
      quota_per_project_enable:
        type: boolean
        description: Enable quota per project
        x-omitempty: true
        x-isnullable: true
      storage_per_project:
        type: integer
        description: The storage quota per project
        x-omitempty: true
        x-isnullable: true
      audit_log_forward_endpoint:
        type: string
        description: The audit log forward endpoint
        x-omitempty: true
        x-isnullable: true
      skip_audit_log_database:
        type: boolean
        description: Skip audit log database
        x-omitempty: true
        x-isnullable: true
      session_timeout:
        type: integer
        description: The session timeout for harbor, in minutes.
        x-omitempty: true
        x-isnullable: true
      scanner_skip_update_pulltime:
        type: boolean
        description: Whether or not to skip update pull time for scanner
        x-omitempty: true
        x-isnullable: true
      banner_message:
        type: string
        description: The banner message for the UI.It is the stringified result of the banner message object
        x-omitempty: true
        x-isnullable: true
  StringConfigItem:
    type: object
    properties:
      value:
        type: string
        x-omitempty: false
        description: The string value of current config item
      editable:
        type: boolean
        x-omitempty: false
        description: The configure item can be updated or not
  BoolConfigItem:
    type: object
    properties:
      value:
        type: boolean
        x-omitempty: false
        description: The boolean value of current config item
      editable:
        type: boolean
        x-omitempty: false
        description: The configure item can be updated or not
  IntegerConfigItem:
    type: object
    properties:
      value:
        type: integer
        x-omitempty: false
        description: The integer value of current config item
      editable:
        type: boolean
        x-omitempty: false
        description: The configure item can be updated or not
  ProjectMemberEntity:
    type: object
    properties:
      id:
        type: integer
        description: the project member id
      project_id:
        type: integer
        description: the project id
      entity_name:
        type: string
        description: the name of the group member.
      role_name:
        type: string
        description: the name of the role
      role_id:
        type: integer
        description: the role id
      entity_id:
        type: integer
        description: 'the id of entity, if the member is a user, it is user_id in user table. if the member is a user group, it is the user group''s ID in user_group table.'
      entity_type:
        type: string
        description: 'the entity''s type, u for user entity, g for group entity.'
  ProjectMember:
    type: object
    properties:
      role_id:
        type: integer
        description: 'The role id 1 for projectAdmin, 2 for developer, 3 for guest, 4 for maintainer'
      member_user:
        $ref: '#/definitions/UserEntity'
      member_group:
        $ref: '#/definitions/UserGroup'
  RoleRequest:
    type: object
    properties:
      role_id:
        type: integer
        description: 'The role id 1 for projectAdmin, 2 for developer, 3 for guest, 4 for maintainer'
  UserEntity:
    type: object
    properties:
      user_id:
        type: integer
        description: The ID of the user.
      username:
        type: string
        description: The name of the user.
  UserProfile:
    type: object
    properties:
      email:
        type: string
      realname:
        type: string
      comment:
        type: string
  UserCreationReq:
    type: object
    properties:
      email:
        type: string
        maxLength: 255
      realname:
        type: string
      comment:
        type: string
      password:
        type: string
      username:
        type: string
        maxLength: 255
  OIDCUserInfo:
    type: object
    properties:
      id:
        type: integer
        format: int
        description: the ID of the OIDC info record
      user_id:
        type: integer
        format: int
        description: the ID of the user
      subiss:
        type: string
        description: the concatenation of sub and issuer in the ID token
      secret:
        type: string
        description: the secret of the OIDC user that can be used for CLI to push/pull artifacts
      creation_time:
        type: string
        format: date-time
        description: The creation time of the OIDC user info record.
      update_time:
        type: string
        format: date-time
        description: The update time of the OIDC user info record.
  UserResp:
    type: object
    properties:
      email:
        type: string
      realname:
        type: string
      comment:
        type: string
      user_id:
        type: integer
        format: int
      username:
        type: string
      sysadmin_flag:
        type: boolean
        x-omitempty: false
      admin_role_in_auth:
        type: boolean
        x-omitempty: false
        description: indicate the admin privilege is grant by authenticator (LDAP), is always false unless it is the current login user
      oidc_user_meta:
        $ref: '#/definitions/OIDCUserInfo'
      creation_time:
        type: string
        format: date-time
        description: The creation time of the user.
      update_time:
        type: string
        format: date-time
        description: The update time of the user.
  UserSysAdminFlag:
    type: object
    properties:
      sysadmin_flag:
        type: boolean
        description: 'true-admin, false-not admin.'
  UserSearch:
    type: object
    properties:
      user_id:
        type: integer
        format: int
        description: The ID of the user.
      username:
        type: string
  PasswordReq:
    type: object
    properties:
      old_password:
        type: string
        description: The user's existing password.
      new_password:
        type: string
        description: New password for marking as to be updated.
  UserSearchRespItem:
    type: object
    properties:
      user_id:
        type: integer
        format: int
        description: The ID of the user.
      username:
        type: string
  Permission:
    type: object
    properties:
      resource:
        type: string
        description: The permission resoruce
      action:
        type: string
        description: The permission action
  Permissions:
    type: object
    properties:
      system:
        type: array
        description: The system level permissions
        items:
          $ref: '#/definitions/Permission'
      project:
        type: array
        description: The project level permissions
        items:
          $ref: '#/definitions/Permission'
  OIDCCliSecretReq:
    type: object
    properties:
      secret:
        type: string
        description: The new secret
  OverallHealthStatus:
    type: object
    description: The system health status
    properties:
      status:
        type: string
        description: The overall health status. It is "healthy" only when all the components' status are "healthy"
      components:
        type: array
        items:
          $ref: '#/definitions/ComponentHealthStatus'
  ComponentHealthStatus:
    type: object
    description: The health status of component
    properties:
      name:
        type: string
        description: The component name
      status:
        type: string
        description: The health status of component. Is either "healthy" or "unhealthy".
      error:
        type: string
        description: (optional) The error message when the status is "unhealthy"
  Statistic:
    type: object
    properties:
      private_project_count:
        type: integer
        format: int64
        description: The count of the private projects
        x-omitempty: false
      private_repo_count:
        type: integer
        format: int64
        description: The count of the private repositories
        x-omitempty: false
      public_project_count:
        type: integer
        format: int64
        description: The count of the public projects
        x-omitempty: false
      public_repo_count:
        type: integer
        format: int64
        description: The count of the public repositories
        x-omitempty: false
      total_project_count:
        type: integer
        format: int64
        description: The count of the total projects, only be seen by the system admin
        x-omitempty: false
      total_repo_count:
        type: integer
        format: int64
        description: The count of the total repositories, only be seen by the system admin
        x-omitempty: false
      total_storage_consumption:
        type: integer
        format: int64
        description: The total storage consumption of blobs, only be seen by the system admin
        x-omitempty: false
  Accessory:
    type: object
    description: 'The accessory of the artifact'
    properties:
      id:
        type: integer
        format: int64
        description: The ID of the accessory
      artifact_id:
        type: integer
        format: int64
        description: The artifact id of the accessory
        x-omitempty: false
      subject_artifact_id:
        type: integer
        format: int64
        description: Going to be deprecated, use repo and digest for insteand. The subject artifact id of the accessory.
      subject_artifact_digest:
        type: string
        description: The subject artifact digest of the accessory
        x-omitempty: false
      subject_artifact_repo:
        type: string
        description: The subject artifact repository name of the accessory
        x-omitempty: false
      size:
        type: integer
        format: int64
        description: The artifact size of the accessory
        x-omitempty: false
      digest:
        type: string
        description: The artifact digest of the accessory
        x-omitempty: false
      type:
        type: string
        description: The artifact size of the accessory
        x-omitempty: false
      icon:
        type: string
        description: The icon of the accessory
        x-omitempty: false
      creation_time:
        type: string
        format: date-time
        description: The creation time of the accessory

  ScanDataExportRequest:
    type: object
    description: The criteria to select the scan data to export.
    properties:
      job_name:
        type: string
        description: Name of the scan data export job
      projects:
        type: array
        items:
          type: integer
          format: int64
        description: A list of one or more projects for which to export the scan data, currently only one project is supported due to performance concerns, but define as array for extension in the future.
      labels:
        type: array
        items:
          type: integer
          format: int64
        description: A list of one or more labels for which to export the scan data, defaults to all if empty
      repositories:
        type: string
        description: A list of repositories for which to export the scan data, defaults to all if empty
      cveIds:
        type: string
        description: CVE-IDs for which to export data. Multiple CVE-IDs can be specified by separating using ',' and enclosed between '{}'. Defaults to all if empty
      tags:
        type: string
        description: A list of tags enclosed within '{}'. Defaults to all if empty
  ScanDataExportJob:
    type: object
    description: The metadata associated with the scan data export job
    properties:
      id:
        type: integer
        format: int64
        description: The id of the scan data export job
  ScanDataExportExecution:
    type: object
    description: The replication execution
    properties:
      id:
        type: integer
        description: The ID of the execution
      user_id:
        type: integer
        description: The ID if the user triggering the export job
      status:
        type: string
        description: The status of the execution
      trigger:
        type: string
        description: The trigger mode
      start_time:
        type: string
        format: date-time
        description: The start time
      end_time:
        type: string
        format: date-time
        description: The end time
      status_text:
        type: string
        x-omitempty: false
        description: The status text
      user_name:
        type: string
        x-omitempty: false
        description: The name of the user triggering the job
      file_present:
        type: boolean
        x-omitempty: false
        description: Indicates whether the export artifact is present in registry
  ScanDataExportExecutionList:
    type: object
    description: The list of scan data export executions
    properties:
      items:
        type: array
        items:
          $ref: '#/definitions/ScanDataExportExecution'
        description: The list of scan data export executions

  WorkerPool:
    type: object
    description: the worker pool of job service
    properties:
      pid:
        type: integer
        description: the process id of jobservice
      worker_pool_id:
        type: string
        description: the id of the worker pool
      start_at:
        type: string
        format: date-time
        description: The start time of the work pool
      heartbeat_at:
        type: string
        format: date-time
        description: The heartbeat time of the work pool
      concurrency:
        type: integer
        description: The concurrency of the work pool
      host:
        type: string
        description: The host of the work pool
  Worker:
    type: object
    description: worker in the pool
    properties:
      id:
        type: string
        description: the id of the worker
      pool_id:
        type: string
        description: the id of the worker pool
      job_name:
        type: string
        description: the name of the running job in the worker
      job_id:
        type: string
        description: the id of the running job in the worker
      start_at:
        type: string
        format: date-time
        description: The start time of the worker
        x-nullable: true
        x-omitempty: true
      check_in:
        type: string
        description: the checkin of the running job in the worker
      checkin_at:
        type: string
        format: date-time
        description: The checkin time of the worker
        x-nullable: true
        x-omitempty: true
  ActionRequest:
    type: object
    description: The request to stop, pause or resume
    properties:
      action:
        type: string
        description: The action of the request, should be stop, pause or resume
        enum:
          - stop
          - pause
          - resume
  JobQueue:
    type: object
    description: the job queue info
    properties:
      job_type:
        type: string
        description: The type of the job queue
      count:
        type: integer
        description: The count of jobs in the job queue
      latency:
        type: integer
        description: The latency the job queue (seconds)
      paused:
        type: boolean
        description: The paused status of the job queue
        x-omitempty: false
  ScheduleTask:
    type: object
    description: the schedule task info
    properties:
      id:
        type: integer
        description: the id of the Schedule task
      vendor_type:
        type: string
        description: the vendor type of the current schedule task
      vendor_id:
        type: integer
        description: the vendor id of the current task
      cron:
        type: string
        description: the cron of the current schedule task
      update_time:
        type: string
        format: date-time
        description: the update time of the schedule task
  SchedulerStatus:
    type: object
    description: the scheduler status
    properties:
      paused:
        type: boolean
        description: if the scheduler is paused
        x-omitempty: false
  SecuritySummary:
    type: object
    description: the security summary
    properties:
      critical_cnt:
        type: integer
        format: int64
        x-omitempty: false
        description: the count of critical vulnerabilities
      high_cnt:
        type: integer
        format: int64
        description: the count of high vulnerabilities
      medium_cnt:
        type: integer
        format: int64
        x-omitempty: false
        description: the count of medium vulnerabilities
      low_cnt:
        type: integer
        format: int64
        x-omitempty: false
        description: the count of low vulnerabilities
      none_cnt:
        type: integer
        format: int64
        description: the count of none vulnerabilities
      unknown_cnt:
        type: integer
        format: int64
        description: the count of unknown vulnerabilities
      total_vuls:
        type: integer
        format: int64
        x-omitempty: false
        description: the count of total vulnerabilities
      scanned_cnt:
        type: integer
        format: int64
        x-omitempty: false
        description: the count of scanned artifacts
      total_artifact:
        type: integer
        format: int64
        x-omitempty: false
        description: the total count of artifacts
      fixable_cnt:
        type: integer
        format: int64
        x-omitempty: false
        description: the count of fixable vulnerabilities
      dangerous_cves:
        type: array
        x-omitempty: true
        description: the list of dangerous CVEs
        items:
          $ref: '#/definitions/DangerousCVE'
      dangerous_artifacts:
        type: array
        x-omitempty: true
        description: the list of dangerous artifacts
        items:
          $ref: '#/definitions/DangerousArtifact'
  DangerousCVE:
    type: object
    description: the dangerous CVE information
    properties:
      cve_id:
        type: string
        description: the cve id
      severity:
        type: string
        description: the severity of the CVE
      cvss_score_v3:
        type: number
        format: float64
        description: the cvss score v3
      desc:
        type: string
        description: the description of the CVE
      package:
        type: string
        description: the package of the CVE
      version:
        type: string
        description: the version of the package
  DangerousArtifact:
    type: object
    description: the dangerous artifact information
    properties:
      project_id:
        type: integer
        format: int64
        description: the project id of the artifact
      repository_name:
        type: string
        description: the repository name of the artifact
      digest:
        type: string
        description: the digest of the artifact
      critical_cnt:
        type: integer
        x-omitempty: false
        description: the count of critical vulnerabilities
      high_cnt:
        type: integer
        format: int64
        x-omitempty: false
        description: the count of high vulnerabilities
      medium_cnt:
        type: integer
        x-omitempty: false
        description: the count of medium vulnerabilities

  VulnerabilityItem:
    type: object
    description: the vulnerability item info
    properties:
      project_id:
        type: integer
        format: int64
        description: the project ID of the artifact
      repository_name:
        type: string
        description: the repository name of the artifact
      digest:
        type: string
        description: the digest of the artifact
      tags:
        type: array
        items:
          type: string
        description: the tags of the artifact
      cve_id:
        type: string
        description: the CVE id of the vulnerability.
      severity:
        type: string
        description: the severity of the vulnerability
      cvss_v3_score:
        type: number
        format: float
        description: the nvd cvss v3 score of the vulnerability
      package:
        type: string
        description: the package of the vulnerability
      version:
        type: string
        description: the version of the package
      fixed_version:
        type: string
        description: the fixed version of the package
      desc:
        type: string
        description: The description of the vulnerability
      links:
        type: array
        items:
          type: string
        description: Links of the vulnerability
  ScanType:
    type: object
    properties:
      scan_type:
        type: string
        description: 'The scan type for the scan request. Two options are currently supported, vulnerability and sbom'
        enum: [ vulnerability, sbom ]
name: Release Note Label Check

# Trigger the workflow on pull requests only
on: 
  pull_request:
    types: [opened, labeled, unlabeled, synchronize]

env:
  GOPROXY: https://proxy.golang.org/
  SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
jobs:
  # Ensures correct release-note labels are set:
  # - At least one label
  # - At most one two the main category labels
  check-label:
    name: Check release-note label set
    runs-on: ubuntu-latest
    steps:
      - uses: mheap/github-action-required-labels@v5
        with:
          mode: minimum
          count: 1
          labels: "release-note/ignore-for-release, release-note/new-feature, release-note/update, release-note/enhancement, release-note/community, release-note/breaking-change, release-note/docs, release-note/infra, release-note/deprecation"

name: <PERSON><PERSON> Nightly <PERSON>an
on:
  schedule:
    - cron: '0 2 * * *' # run at 2 AM UTC


jobs:
  nightly-scan:
    name: <PERSON><PERSON>an nightly
    strategy:
      fail-fast: false
      matrix:
        # maintain the versions of harbor that need to be actively
        # security scanned
        versions: [dev, v2.11.0-dev]
        # list of images that need to be scanned
        images: [harbor-core, harbor-db, harbor-exporter, harbor-jobservice, harbor-log, harbor-portal, harbor-registryctl, prepare]
    permissions:
      security-events: write  # for github/codeql-action/upload-sarif to upload SARIF results

    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'docker.io/goharbor/${{ matrix.images }}:${{ matrix.versions }}'
          severity: 'CRITICAL,HIGH'
          format: 'template'
          template: '@/contrib/sarif.tpl'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results.sarif'

SCANNER_LOG_LEVEL={{log_level}}
SCANNER_REDIS_URL={{trivy_redis_url}}
SCANNER_STORE_REDIS_URL={{trivy_redis_url}}
SCANNER_STORE_REDIS_NAMESPACE=harbor.scanner.trivy:store
SCANNER_JOB_QUEUE_REDIS_URL={{trivy_redis_url}}
SCANNER_JOB_QUEUE_REDIS_NAMESPACE=harbor.scanner.trivy:job-queue
SCANNER_TRIVY_CACHE_DIR=/home/<USER>/.cache/trivy
SCANNER_TRIVY_REPORTS_DIR=/home/<USER>/.cache/reports
SCANNER_TRIVY_VULN_TYPE=os,library
SCANNER_TRIVY_SEVERITY=UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL
SCANNER_TRIVY_IGNORE_UNFIXED={{trivy_ignore_unfixed}}
SCANNER_TRIVY_SKIP_UPDATE={{trivy_skip_update}}
SCANNER_TRIVY_SKIP_JAVA_DB_UPDATE={{trivy_skip_java_db_update}}
SCANNER_TRIVY_OFFLINE_SCAN={{trivy_offline_scan}}
SCANNER_TRIVY_SECURITY_CHECKS={{trivy_security_check}}
SCANNER_TRIVY_GITHUB_TOKEN={{trivy_github_token}}
SCANNER_TRIVY_INSECURE={{trivy_insecure}}
SCANNER_TRIVY_TIMEOUT={{trivy_timeout}}
HTTP_PROXY={{trivy_http_proxy}}
HTTPS_PROXY={{trivy_https_proxy}}
NO_PROXY={{trivy_no_proxy}}
{%if internal_tls.enabled %}
SCANNER_API_SERVER_ADDR=:8443
SCANNER_API_SERVER_TLS_KEY=/etc/harbor/ssl/trivy_adapter.key
SCANNER_API_SERVER_TLS_CERTIFICATE=/etc/harbor/ssl/trivy_adapter.crt
{% endif %}

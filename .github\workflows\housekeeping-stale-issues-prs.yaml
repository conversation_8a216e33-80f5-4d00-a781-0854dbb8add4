name: Housekeeping - Close stale issues and PRs
on:
  schedule:
    - cron: '0 9 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9.0.0
        with:
          stale-issue-message: 'This issue is being marked stale due to a period of inactivity. If this issue is still relevant, please comment or remove the stale label. Otherwise, this issue will close in 30 days.'
          stale-pr-message: 'This PR is being marked stale due to a period of inactivty. If this PR is still relevant, please comment or remove the stale label. Otherwise, this PR will close in 30 days.'
          close-issue-message: 'This issue was closed because it has been stalled for 30 days with no activity. If this issue is still relevant, please re-open a new issue.'
          close-pr-message: 'This PR was closed because it has been stalled for 30 days with no activity. If this PR is still relevant, please re-open a new PR against main.'
          days-before-issue-stale: 60
          days-before-pr-stale: 60
          days-before-issue-close: 30
          days-before-pr-close: 30
          # Don't add stale label to PRs / issues with milestones "upcoming" attached.
          exempt-milestones: "upcoming"
          # Don't add stale label to PRs / issues with this label
          exempt-issue-labels: 'never-stale, kind/requirement'
          exempt-pr-labels: 'never-stale, kind/requirement'
          # Make it 1000 to clean up a bit then wen can lower it
          operations-per-run: 1000

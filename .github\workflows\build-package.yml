name: "Build Package Workflow"
env:
  DOCKER_COMPOSE_VERSION: 1.23.0

on:
  push:
    branches:
      - main
      - release-*

jobs:
  BUILD_PACKAGE:
    env:
        BUILD_PACKAGE: true
    runs-on:
      - ubuntu-22.04
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4.1.0
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: Set up Go 1.22
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.8
        id: go
      - name: Setup Docker
        uses: docker-practice/actions-setup-docker@master
        with:
          docker_version: 20.10
          docker_channel: stable
      - uses: actions/checkout@v3
      - uses: jitterbit/get-changed-files@v1
        id: changed-files
        with:
          format: space-delimited
          token: ${{ secrets.GITHUB_TOKEN }}
      - uses: actions/checkout@v3
        with:
          path: src/github.com/goharbor/harbor
      - name: Build Base Image
        if: |
            contains(steps.changed-files.outputs.modified, 'Dockerfile.base') ||
            contains(steps.changed-files.outputs.modified, 'VERSION') ||
            contains(steps.changed-files.outputs.modified, '.buildbaselog') ||
            github.ref == 'refs/heads/main'
        run: |
          set -x
          echo "BUILD_BASE=true" >> $GITHUB_ENV
      - name: Build Package
        run: |
          set -x
          env
          df -h
          harbor_target_bucket=""
          target_branch="$(echo ${GITHUB_REF#refs/heads/})"
          harbor_offline_build_bundle=""
          harbor_online_build_bundle=""
          harbor_logs_bucket="harbor-ci-logs"
          harbor_builds_bucket="harbor-builds"
          harbor_releases_bucket="harbor-releases"
          harbor_ci_pipeline_store_bucket="harbor-ci-pipeline-store/latest"
          # the target release version is the version of next release(RC or GA). It needs to be updated on creating new release branch.
          target_release_version=$(cat ./VERSION)
          Harbor_Package_Version=$target_release_version-'build.'$GITHUB_RUN_NUMBER

          if [[ $target_branch == "main" ]]; then
            Harbor_Assets_Version=$Harbor_Package_Version
            harbor_target_bucket=$harbor_builds_bucket
          else
            Harbor_Assets_Version=$target_release_version
            harbor_target_bucket=$harbor_releases_bucket/$target_branch
          fi

          if [[ $target_branch == "release-"* ]]; then
            Harbor_Build_Base_Tag=$target_release_version
          else
            Harbor_Build_Base_Tag=dev
          fi

          build_base_params=" BUILD_BASE=false"
          cd src/github.com/goharbor/harbor
          if [ -z "$BUILD_BASE"  ] || [ "$BUILD_BASE" != "true"  ]; then
            echo "Do not need to build base images!"
          else
            build_base_params=" BUILD_BASE=true PUSHBASEIMAGE=true REGISTRYUSER=\"${{ secrets.DOCKER_HUB_USERNAME }}\" REGISTRYPASSWORD=\"${{ secrets.DOCKER_HUB_PASSWORD }}\""
          fi
          sudo make package_offline GOBUILDTAGS="include_oss include_gcs" BASEIMAGETAG=${Harbor_Build_Base_Tag} VERSIONTAG=${Harbor_Assets_Version} PKGVERSIONTAG=${Harbor_Package_Version} TRIVYFLAG=true HTTPPROXY= ${build_base_params}
          sudo make package_online GOBUILDTAGS="include_oss include_gcs" BASEIMAGETAG=${Harbor_Build_Base_Tag} VERSIONTAG=${Harbor_Assets_Version} PKGVERSIONTAG=${Harbor_Package_Version} TRIVYFLAG=true HTTPPROXY= ${build_base_params}
          harbor_offline_build_bundle=$(basename harbor-offline-installer-*.tgz)
          harbor_online_build_bundle=$(basename harbor-online-installer-*.tgz)
          echo "Package name is: $harbor_offline_build_bundle"
          echo "Package name is: $harbor_online_build_bundle"

          source tests/ci/build_util.sh
          cp ${harbor_offline_build_bundle}                 harbor-offline-installer-latest.tgz
          cp ${harbor_online_build_bundle}                  harbor-online-installer-latest.tgz
          uploader ${harbor_offline_build_bundle}           $harbor_target_bucket
          uploader ${harbor_online_build_bundle}            $harbor_target_bucket
          uploader harbor-offline-installer-latest.tgz      $harbor_target_bucket
          uploader harbor-online-installer-latest.tgz       $harbor_target_bucket
          echo "BUILD_BUNDLE=$harbor_offline_build_bundle" >> $GITHUB_ENV

          publishImage $target_branch $Harbor_Assets_Version "${{ secrets.DOCKER_HUB_USERNAME }}" "${{ secrets.DOCKER_HUB_PASSWORD }}"
